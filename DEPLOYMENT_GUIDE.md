# 🚀 A.T.L.A.S Enhanced Deployment Guide

## 📋 Prerequisites

### **System Requirements**
- **Python**: 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended for optimal performance)
- **Storage**: 2GB free space for system and data
- **Network**: Stable internet connection for real-time market data

### **Required API Keys**
```bash
# Trading APIs
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Paper trading

# Market Data
FMP_API_KEY=your_fmp_key

# AI Services
OPENAI_API_KEY=your_openai_key

# Optional: Enhanced Predictions
PREDICTO_API_KEY=your_predicto_key  # Optional but recommended
```

## 🔧 Installation Methods

### **Method 1: Quick Start (Recommended)**

```bash
# Clone the repository
git clone https://github.com/your-repo/atlas-enhanced.git
cd atlas-enhanced

# Create virtual environment
python -m venv atlas_env
source atlas_env/bin/activate  # On Windows: atlas_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys

# Initialize the system
python main.py --setup

# Start A.T.L.A.S
python main.py
```

### **Method 2: Docker Deployment**

```bash
# Clone and navigate
git clone https://github.com/your-repo/atlas-enhanced.git
cd atlas-enhanced

# Create environment file
cp .env.example .env
# Edit .env with your API keys

# Build and run with Docker Compose
docker-compose up --build

# Access A.T.L.A.S at http://localhost:8080
```

### **Method 3: Production Deployment**

```bash
# For production environments
git clone https://github.com/your-repo/atlas-enhanced.git
cd atlas-enhanced

# Install with production dependencies
pip install -r requirements-prod.txt

# Set production environment variables
export ATLAS_ENV=production
export ATLAS_LOG_LEVEL=INFO
export ATLAS_ENABLE_COMPLIANCE=true

# Run with production settings
python main.py --production
```

## ⚙️ Configuration

### **Basic Configuration**

Edit `streamlined/config.py` or use environment variables:

```python
# Core Settings
ATLAS_ENV = "development"  # development, staging, production
ATLAS_LOG_LEVEL = "DEBUG"  # DEBUG, INFO, WARNING, ERROR
ATLAS_PORT = 8080

# Trading Settings
PAPER_TRADING = True  # Always start with paper trading
DEFAULT_ACCOUNT_SIZE = 25000
MAX_DAILY_LOSS_PERCENT = 3.0
MAX_POSITION_SIZE_PERCENT = 20.0

# AI Settings
MENTOR_MODE = True  # Enable conversational mentor features
ENABLE_MULTI_AGENT = True  # Enable multi-agent analysis
ENABLE_RL_EXECUTION = True  # Enable RL-optimized execution
ENABLE_COMPLIANCE = True  # Enable compliance monitoring

# Performance Settings
CACHE_TTL_SECONDS = 300
MAX_CONCURRENT_TASKS = 10
PERFORMANCE_MONITORING = True
```

### **Advanced Configuration**

```python
# Multi-Agent Weights (must sum to 1.0)
AGENT_WEIGHTS = {
    "technical": 0.35,
    "risk": 0.30,
    "sentiment": 0.20,
    "execution": 0.15
}

# Risk Management Thresholds
RISK_THRESHOLDS = {
    "vix_danger_level": 40.0,
    "min_confidence": 0.70,
    "max_correlation": 0.85,
    "max_leverage": 2.0
}

# Compliance Settings
COMPLIANCE_LEVEL = "institutional"  # basic, institutional, regulatory
AUDIT_RETENTION_DAYS = 365
ENABLE_AUDIT_TRAIL = True

# Performance Optimization
ENABLE_CACHING = True
CACHE_MAX_SIZE = 1000
ENABLE_PERFORMANCE_MONITORING = True
AUTO_OPTIMIZATION = True
```

## 🚀 Startup Process

### **1. System Initialization**

```bash
# First-time setup
python main.py --setup

# This will:
# - Create necessary databases
# - Initialize compliance tables
# - Set up performance monitoring
# - Load trading books for RAG
# - Validate API connections
```

### **2. Health Check**

```bash
# Verify system health
curl http://localhost:8080/health

# Expected response:
{
  "status": "healthy",
  "version": "2.0.0-enhanced",
  "features": {
    "mentor_mode": true,
    "multi_agent": true,
    "rl_execution": true,
    "compliance": true,
    "performance_monitoring": true
  },
  "api_status": {
    "alpaca": "connected",
    "fmp": "connected",
    "openai": "connected",
    "predicto": "connected"
  }
}
```

### **3. Feature Verification**

```bash
# Test conversational interface
curl -X POST http://localhost:8080/api/v1/chat/cot \
  -H "Content-Type: application/json" \
  -d '{"message": "I want to make $100 this week", "user_context": {"account_size": 25000}}'

# Test multi-agent analysis
curl -X POST http://localhost:8080/api/v1/cot/analyze-symbol \
  -H "Content-Type: application/json" \
  -d '{"symbol": "AAPL", "account_size": 25000}'

# Test performance monitoring
curl http://localhost:8080/api/v1/system/performance
```

## 🔒 Security & Compliance

### **Security Best Practices**

```bash
# 1. Secure API key storage
export ALPACA_API_KEY="your_key_here"  # Never hardcode in files
export OPENAI_API_KEY="your_key_here"

# 2. Enable HTTPS in production
export ATLAS_USE_HTTPS=true
export ATLAS_SSL_CERT_PATH="/path/to/cert.pem"
export ATLAS_SSL_KEY_PATH="/path/to/key.pem"

# 3. Set up authentication (production)
export ATLAS_ENABLE_AUTH=true
export ATLAS_JWT_SECRET="your_jwt_secret"

# 4. Enable audit logging
export ATLAS_AUDIT_LEVEL="full"
export ATLAS_LOG_RETENTION_DAYS=365
```

### **Compliance Configuration**

```python
# Enable full compliance monitoring
COMPLIANCE_SETTINGS = {
    "level": "regulatory",  # basic, institutional, regulatory
    "daily_loss_limit": 3.0,
    "position_size_limit": 20.0,
    "concentration_limit": 25.0,
    "leverage_limit": 2.0,
    "trade_frequency_limit": 100,
    "audit_trail": True,
    "violation_alerts": True
}
```

## 📊 Monitoring & Maintenance

### **Performance Monitoring**

```bash
# Real-time performance dashboard
curl http://localhost:8080/api/v1/system/performance

# Compliance report
curl http://localhost:8080/api/v1/compliance/report?user_id=user123&days=30

# RL execution analytics
curl http://localhost:8080/api/v1/execution/analytics
```

### **Log Management**

```bash
# View system logs
tail -f logs/atlas.log

# View audit logs
tail -f logs/audit.log

# View performance logs
tail -f logs/performance.log

# Log rotation (production)
logrotate /etc/logrotate.d/atlas
```

### **Database Maintenance**

```bash
# Backup compliance database
sqlite3 atlas_compliance.db ".backup atlas_compliance_backup.db"

# Cleanup old audit records (older than 1 year)
python -c "
from streamlined.compliance_audit_system import ComplianceEngine
engine = ComplianceEngine('atlas_compliance.db')
engine.cleanup_old_records(days=365)
"

# Optimize performance
python -c "
from streamlined.performance_optimizer import PerformanceOptimizer
optimizer = PerformanceOptimizer()
optimizer.optimize_system_performance()
"
```

## 🔧 Troubleshooting

### **Common Issues**

1. **API Connection Failures**
```bash
# Check API key validity
python -c "
from streamlined.market_data import MarketDataService
service = MarketDataService()
print(service.test_connection())
"
```

2. **Performance Issues**
```bash
# Check system health
curl http://localhost:8080/api/v1/system/health

# Optimize performance
curl -X POST http://localhost:8080/api/v1/system/optimize
```

3. **Compliance Violations**
```bash
# Check compliance status
curl http://localhost:8080/api/v1/compliance/status?user_id=user123
```

### **Support & Updates**

- **Documentation**: Full API docs at `/docs` endpoint
- **Health Monitoring**: Real-time status at `/health`
- **Performance Dashboard**: System metrics at `/api/v1/system/performance`
- **Compliance Dashboard**: Regulatory status at `/api/v1/compliance/dashboard`

## 🎯 Next Steps

1. **Start with Paper Trading**: Always begin with paper trading to learn the system
2. **Complete Tutorial**: Use the built-in tutorial at `/tutorial`
3. **Read Documentation**: Comprehensive guides at `/docs`
4. **Join Community**: Connect with other A.T.L.A.S users
5. **Monitor Performance**: Regular check system health and compliance

**🎉 Congratulations! Your enhanced A.T.L.A.S system is ready for intelligent, mentor-guided trading!**
