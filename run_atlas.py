#!/usr/bin/env python3
"""
A.T.L.A.S Next-Generation Profit Optimization System Launcher
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add streamlined to path
streamlined_path = Path(__file__).parent / "streamlined"
sys.path.insert(0, str(streamlined_path))

def main():
    """Main launcher for A.T.L.A.S system"""
    
    print("🚀 Starting A.T.L.A.S Next-Generation Profit Optimization System")
    print("=" * 70)
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📈 Profit Maximization Engine Active")
    print("🤖 Multi-Agent AI Intelligence")
    print("🔄 Trade Recycling System")
    print("📚 Educational Paper Trading Mode")
    print("=" * 70)
    
    try:
        # Test core imports
        print("🔧 Testing system components...")
        
        from streamlined.profit_maximization_engine import DynamicProfitMaximizationEngine
        from streamlined.alpha_diversification_engine import AlphaDiversificationEngine
        from streamlined.trade_recycling_engine import TradeRecyclingEngine
        print("✅ Profit optimization engines loaded")
        
        from streamlined.cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator
        print("✅ Trading orchestrator loaded")
        
        from streamlined.atlas_server import app
        print("✅ Server application loaded")
        
        print("🎉 All components loaded successfully!")
        print()
        
        # Start the server
        print("🌐 Starting web server on http://localhost:8080")
        print("📖 API Documentation: http://localhost:8080/docs")
        print("🔍 Health Check: http://localhost:8080/api/v1/health")
        print("🚀 Profit Optimization: http://localhost:8080/api/v1/profit-optimization")
        print()
        print("Press Ctrl+C to stop the server")
        print("=" * 70)
        
        # Run the server
        uvicorn.run(
            "streamlined.atlas_server:app",
            host="0.0.0.0",
            port=8081,
            reload=False,  # Disable reload to avoid import issues
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Please ensure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
