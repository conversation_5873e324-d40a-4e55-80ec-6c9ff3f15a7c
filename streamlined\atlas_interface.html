<!DOCTYPE html>
<html>
<head>
    <title>A.T.L.A.S Next-Generation Profit Optimization System</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .header h1 {
            font-size: 3em;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2em;
            color: #a0a0a0;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .status-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-card h3 {
            color: #00ff88;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .status-card ul {
            list-style: none;
        }
        .status-card li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .status-card li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .api-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .api-section h2 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        .api-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .api-button:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,212,255,0.3);
        }
        .profit-optimizer {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        .profit-optimizer:hover {
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
        }
        .test-section {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-section h3 {
            color: #00ff88;
            margin-bottom: 15px;
        }
        .test-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .form-group label {
            color: #a0a0a0;
            font-weight: 600;
        }
        .form-group input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #result {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }
        /* Enhanced A.T.L.A.S Trading Interface */
        .atlas-trading-interface {
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .atlas-trading-interface::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .trading-container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(0, 20, 40, 0.7);
            border-radius: 25px;
            padding: 25px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(0, 255, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .trading-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .welcome-message {
            flex: 1;
        }

        .message-bubble {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            padding: 12px 16px;
            color: #00ffff;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .market-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 255, 0.2);
            min-width: 120px;
        }

        .market-symbol {
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .market-price {
            color: #00ffff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .market-change {
            font-size: 14px;
            font-weight: bold;
        }

        .market-change.positive {
            color: #00ff88;
        }

        .market-change.negative {
            color: #ff4444;
        }

        .market-details {
            margin-top: 10px;
            font-size: 12px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .detail-row .label {
            color: #888;
        }

        .detail-row .value {
            color: #00ffff;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-btn {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 12px;
            padding: 12px 16px;
            color: #00ffff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .chart-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 255, 255, 0.2);
        }

        #tradingChart {
            width: 100%;
            height: 200px;
            border-radius: 10px;
        }

        .volume-chart {
            margin-top: 10px;
        }

        #volumeChart {
            width: 100%;
            height: 60px;
            border-radius: 5px;
        }

        .chat-messages {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            min-height: 200px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(0, 255, 255, 0.1);
        }

        .chat-input-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 255, 0.2);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-container input {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 10px;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 14px;
        }

        .input-container input::placeholder {
            color: #888;
        }

        .input-container input:focus {
            outline: none;
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
        }

        .send-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .send-icon {
            font-size: 16px;
        }

        /* Hide the old sections when using the new interface */
        .atlas-trading-interface ~ .hero-section,
        .atlas-trading-interface ~ .features-section,
        .atlas-trading-interface ~ .api-section {
            display: none;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
        }
        .user-message {
            align-self: flex-end;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
        }
        .assistant-message {
            align-self: flex-start;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .message-content li {
            margin: 5px 0;
        }
        .chat-input-container {
            display: flex;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        #chatInput {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 16px;
            margin-right: 10px;
        }
        #chatInput:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        #chatInput::placeholder {
            color: rgba(255,255,255,0.5);
        }
        #sendButton {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        #sendButton:hover {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            transform: translateY(-2px);
        }
        #sendButton:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 10px;
            padding: 15px;
            background: rgba(255,255,255,0.03);
            border-top: 1px solid rgba(255,255,255,0.1);
            flex-wrap: wrap;
        }
        .quick-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .quick-btn:hover {
            background: rgba(0,212,255,0.2);
            border-color: #00d4ff;
            transform: translateY(-2px);
        }
        .profit-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            border-color: #ff6b35;
        }
        .profit-btn:hover {
            background: linear-gradient(45deg, #f7931e, #ff6b35);
        }

        /* Market Widget */
        .market-widget {
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 12px;
            margin: 15px;
            overflow: hidden;
        }
        .widget-header {
            background: rgba(0,212,255,0.2);
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
            color: #00d4ff;
        }
        .widget-content {
            padding: 15px;
        }
        .market-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .market-item:last-child {
            border-bottom: none;
        }
        .symbol {
            font-weight: bold;
            color: #00d4ff;
            min-width: 50px;
        }
        .price {
            font-weight: bold;
            color: white;
        }
        .change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .change.positive {
            background: rgba(34,197,94,0.2);
            color: #22c55e;
        }
        .change.negative {
            background: rgba(239,68,68,0.2);
            color: #ef4444;
        }

        /* Communication Mode Selector */
        .mode-selector {
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .mode-selector label {
            color: #00d4ff;
            font-weight: bold;
            font-size: 14px;
        }
        .mode-selector select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
            flex: 1;
        }
        .mode-selector select:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0,212,255,0.3);
        }
        .mode-selector option {
            background: #1a1a2e;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 A.T.L.A.S</h1>
            <p>Next-Generation Profit Optimization System</p>
            <p><strong>Advanced Trading & Learning Analysis System</strong></p>
            <div style="margin-top: 20px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 10px; border: 1px solid rgba(0,255,136,0.3);">
                <strong>🎯 Status: FULLY OPERATIONAL</strong><br>
                📚 Educational Paper Trading Mode | 🤖 AI-Enhanced Intelligence | 🔄 Profit Optimization Active
            </div>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🎯 Profit Optimization Features</h3>
                <ul>
                    <li>Enhanced TTM Squeeze Analysis</li>
                    <li>Multi-Strategy Alpha Generation</li>
                    <li>Intelligent Trade Recycling</li>
                    <li>Dynamic Risk Management</li>
                    <li>Real-time RL Optimization</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>🤖 AI Integration Status</h3>
                <ul>
                    <li>OpenAI GPT-4 Active</li>
                    <li>Predicto API Integrated</li>
                    <li>Multi-Agent Coordination</li>
                    <li>Chain-of-Thought Analysis</li>
                    <li>Educational Insights</li>
                </ul>
            </div>
            <div class="status-card">
                <h3>📊 Market Data Sources</h3>
                <ul>
                    <li>Alpaca Markets API</li>
                    <li>Financial Modeling Prep</li>
                    <li>Predicto Deep Learning</li>
                    <li>Real-time Quote Data</li>
                    <li>Historical Analysis</li>
                </ul>
            </div>
        </div>

        <div class="api-section">
            <h2>🚀 API Endpoints</h2>
            <p>Access A.T.L.A.S powerful trading intelligence through these endpoints:</p>
            <div class="api-buttons">
                <a href="/docs" class="api-button">📖 API Documentation</a>
                <a href="/api/v1/health" class="api-button">🔍 Health Check</a>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">🎯 Test Profit Optimization</button>
                <a href="/api/v1/cot/dashboard" class="api-button">📊 CoT Dashboard</a>
            </div>
        </div>

        <!-- Enhanced A.T.L.A.S Trading Interface -->
        <div class="atlas-trading-interface">
            <div class="trading-container">
                <!-- Header with Market Data -->
                <div class="trading-header">
                    <div class="welcome-message">
                        <div class="message-bubble">
                            Hello! How can I assist you today?
                        </div>
                    </div>
                    <div class="market-display">
                        <div class="market-symbol">XXXX</div>
                        <div class="market-price">237.15</div>
                        <div class="market-change positive">+3.12%</div>
                        <div class="market-details">
                            <div class="detail-row">
                                <span class="label">Open</span>
                                <span class="value">234.50</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">High</span>
                                <span class="value">238.75</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Low</span>
                                <span class="value">233.50</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Volume</span>
                                <span class="value">25.62M</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Action Buttons -->
                <div class="action-buttons">
                    <button onclick="quickAction('Get a stock quote')" class="action-btn">Get a stock quote</button>
                    <button onclick="quickAction('Show technical indicators')" class="action-btn">Show technical indicators</button>
                    <button onclick="quickAction('Analyze trends')" class="action-btn">Analyze trends</button>
                </div>

                <!-- Embedded Trading Chart -->
                <div class="chart-section">
                    <canvas id="tradingChart" width="400" height="200"></canvas>
                    <div class="volume-chart">
                        <canvas id="volumeChart" width="400" height="60"></canvas>
                    </div>
                </div>

                <!-- Chat Messages Area -->
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be dynamically added here -->
                </div>

                <!-- Chat Input -->
                <div class="chat-input-section">
                    <div class="input-container">
                        <input type="text" id="chatInput" placeholder="Enter a message..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()" class="send-button">
                            <span class="send-icon">➤</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Test: Profit Optimization System</h3>
            <div class="test-form">
                <div class="form-group">
                    <label for="accountSize">Account Size ($)</label>
                    <input type="number" id="accountSize" value="100000" min="1000" step="1000">
                </div>
                <button onclick="testProfitOptimization()" class="api-button profit-optimizer">
                    🚀 Generate Profit Optimization Plan
                </button>
            </div>
            <div id="result"></div>
        </div>
    </div>

    <script>
        // Advanced A.T.L.A.S Chat functionality with intelligent routing
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessageToChat('user', message);
            chatInput.value = '';

            // Show loading state
            setLoadingState(true);

            try {
                // Intelligent routing based on message content
                const route = determineRoute(message);
                let response;

                switch (route.type) {
                    case 'profit_optimization':
                        response = await handleProfitOptimization(message, route.params);
                        break;
                    case 'symbol_analysis':
                        response = await handleSymbolAnalysis(message, route.params);
                        break;
                    case 'trading_plan':
                        response = await handleTradingPlan(message, route.params);
                        break;
                    case 'market_data':
                        response = await handleMarketData(message, route.params);
                        break;
                    case 'technical_scan':
                        response = await handleTechnicalScan(message, route.params);
                        break;
                    case 'portfolio_check':
                        response = await handlePortfolioCheck(message);
                        break;
                    case 'education':
                        response = await handleEducation(message);
                        break;
                    default:
                        response = await handleGeneralChat(message);
                }

                // Display the response
                if (response.success) {
                    addMessageToChat('assistant', response.message);

                    // Add any additional data visualizations
                    if (response.data) {
                        displayAdditionalData(response.data);
                    }
                } else {
                    addMessageToChat('assistant', `❌ ${response.error || 'I encountered an issue processing your request.'}`);
                }

            } catch (error) {
                addMessageToChat('assistant', `❌ Network error: ${error.message}`);
            } finally {
                setLoadingState(false);
            }
        }

        function addMessageToChat(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            if (type === 'user') {
                messageContent.innerHTML = `<strong>👤 You:</strong> ${content}`;
            } else {
                messageContent.innerHTML = `<strong>🤖 A.T.L.A.S:</strong> ${content.replace(/\n/g, '<br>')}`;
            }

            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoadingState(isLoading) {
            const sendButton = document.getElementById('sendButton');
            const sendIcon = document.getElementById('sendIcon');
            const loadingIcon = document.getElementById('loadingIcon');
            const chatInput = document.getElementById('chatInput');

            sendButton.disabled = isLoading;
            chatInput.disabled = isLoading;

            if (isLoading) {
                sendIcon.style.display = 'none';
                loadingIcon.style.display = 'inline';
                addMessageToChat('assistant', '🤔 Thinking...');
            } else {
                sendIcon.style.display = 'inline';
                loadingIcon.style.display = 'none';
                // Remove the "Thinking..." message
                const messages = document.querySelectorAll('.message');
                const lastMessage = messages[messages.length - 1];
                if (lastMessage && lastMessage.textContent.includes('🤔 Thinking...')) {
                    lastMessage.remove();
                }
            }
        }

        // Intelligent routing system
        function determineRoute(message) {
            const msg = message.toLowerCase();

            // Profit optimization patterns
            if (msg.includes('make me') && (msg.includes('$') || msg.includes('money') || msg.includes('profit'))) {
                const amount = extractAmount(message);
                return { type: 'profit_optimization', params: { goal: message, amount } };
            }

            // Symbol analysis patterns
            const symbolMatch = message.match(/\b[A-Z]{1,5}\b/);
            if (symbolMatch && (msg.includes('analyze') || msg.includes('look at') || msg.includes('check') || msg.includes('what') || msg.includes('how'))) {
                return { type: 'symbol_analysis', params: { symbol: symbolMatch[0] } };
            }

            // Trading plan patterns
            if (msg.includes('plan') || msg.includes('strategy') || msg.includes('trade') || msg.includes('position')) {
                return { type: 'trading_plan', params: { request: message } };
            }

            // Market data patterns
            if (msg.includes('quote') || msg.includes('price') || msg.includes('market') || msg.includes('data')) {
                return { type: 'market_data', params: { request: message } };
            }

            // Technical scan patterns
            if (msg.includes('scan') || msg.includes('find') || msg.includes('search') || msg.includes('momentum') || msg.includes('breakout')) {
                return { type: 'technical_scan', params: { request: message } };
            }

            // Portfolio patterns
            if (msg.includes('portfolio') || msg.includes('positions') || msg.includes('account') || msg.includes('balance')) {
                return { type: 'portfolio_check', params: {} };
            }

            // Education patterns
            if (msg.includes('explain') || msg.includes('learn') || msg.includes('teach') || msg.includes('how to') || msg.includes('what is')) {
                return { type: 'education', params: { question: message } };
            }

            return { type: 'general_chat', params: { message } };
        }

        function extractAmount(message) {
            const match = message.match(/\$(\d+(?:,\d{3})*(?:\.\d{2})?)/);
            return match ? parseFloat(match[1].replace(/,/g, '')) : null;
        }

        // Handler functions for different request types
        async function handleProfitOptimization(message, params) {
            try {
                const response = await fetch('/api/v1/profit-optimization', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        account_size: 50000, // Default account size
                        active_positions: [],
                        closed_positions: []
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `🎯 **Profit Optimization Plan Generated!**\n\n`;
                    message += `💰 **Target**: ${params.goal}\n`;
                    message += `📊 **Strategy**: ${data.strategy_summary}\n`;
                    message += `⚡ **Top Opportunities**:\n`;

                    data.opportunities.slice(0, 3).forEach((opp, i) => {
                        message += `${i + 1}. **${opp.symbol}** - ${opp.setup_type} (${opp.confidence}% confidence)\n`;
                        message += `   💡 ${opp.reasoning}\n`;
                    });

                    message += `\n🛡️ **Risk Management**: ${data.risk_summary}`;

                    return { success: true, message, data: data.opportunities };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleSymbolAnalysis(message, params) {
            try {
                const response = await fetch('/api/v1/cot/analyze-symbol', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: params.symbol,
                        account_size: 50000
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `📈 **${params.symbol} Analysis Complete!**\n\n`;
                    message += `🎯 **Signal**: ${data.signal} (${data.confidence}% confidence)\n`;
                    message += `💭 **Chain of Thought**:\n${data.reasoning}\n\n`;
                    message += `📊 **Technical Summary**: ${data.technical_summary}\n`;
                    message += `💰 **Position Recommendation**: ${data.position_recommendation}\n`;
                    message += `🛡️ **Risk Level**: ${data.risk_assessment}`;

                    return { success: true, message, data: data.technical_data };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleTradingPlan(message, params) {
            try {
                const response = await fetch('/api/v1/cot/create-plan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_request: params.request,
                        account_size: 50000,
                        risk_tolerance: 'moderate'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    let message = `📋 **Trading Plan Created!**\n\n`;
                    message += `🎯 **Objective**: ${data.objective}\n`;
                    message += `💰 **Capital Allocation**: ${data.capital_allocation}\n`;
                    message += `📊 **Strategy**: ${data.strategy_summary}\n`;
                    message += `🛡️ **Risk Management**: ${data.risk_rules}\n\n`;
                    message += `⚡ **Immediate Actions**:\n`;
                    data.action_items.forEach((action, i) => {
                        message += `${i + 1}. ${action}\n`;
                    });

                    return { success: true, message, data: data.detailed_plan };
                } else {
                    return { success: false, error: data.error };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleMarketData(message, params) {
            try {
                // Extract symbol if present
                const symbolMatch = message.match(/\b[A-Z]{1,5}\b/);
                if (symbolMatch) {
                    const symbol = symbolMatch[0];
                    const response = await fetch(`/api/v1/quote/${symbol}`);
                    const data = await response.json();

                    let message = `📊 **${symbol} Market Data**\n\n`;
                    message += `💰 **Price**: $${data.price} (${data.change >= 0 ? '+' : ''}${data.change_percent}%)\n`;
                    message += `📈 **High**: $${data.high} | **Low**: $${data.low}\n`;
                    message += `📊 **Volume**: ${data.volume.toLocaleString()}\n`;
                    message += `⏰ **Last Updated**: ${new Date(data.timestamp).toLocaleTimeString()}`;

                    return { success: true, message, data };
                } else {
                    return { success: true, message: "📊 Please specify a stock symbol to get market data (e.g., 'Get AAPL price')" };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleTechnicalScan(message, params) {
            try {
                const response = await fetch('/api/v1/scan', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        scan_type: 'comprehensive',
                        symbols: ['SPY', 'QQQ', 'AAPL', 'TSLA', 'MSFT', 'NVDA']
                    })
                });

                const data = await response.json();

                let message = `🔍 **Technical Scan Results**\n\n`;
                message += `⚡ **Momentum Plays**:\n`;
                data.momentum_plays.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.signal} (${stock.score}/10)\n`;
                });

                message += `\n📈 **Breakout Candidates**:\n`;
                data.breakout_candidates.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.pattern} (${stock.probability}%)\n`;
                });

                message += `\n🎯 **TTM Squeeze Setups**:\n`;
                data.ttm_squeeze.slice(0, 3).forEach(stock => {
                    message += `• **${stock.symbol}** - ${stock.stage} (${stock.timeframe})\n`;
                });

                return { success: true, message, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handlePortfolioCheck(message) {
            try {
                const [portfolioResponse, positionsResponse] = await Promise.all([
                    fetch('/api/v1/portfolio'),
                    fetch('/api/v1/positions')
                ]);

                const portfolio = await portfolioResponse.json();
                const positions = await positionsResponse.json();

                let message = `💼 **Portfolio Summary**\n\n`;
                message += `💰 **Account Value**: $${portfolio.total_value.toLocaleString()}\n`;
                message += `📈 **Day P&L**: ${portfolio.day_pnl >= 0 ? '+' : ''}$${portfolio.day_pnl.toLocaleString()}\n`;
                message += `📊 **Buying Power**: $${portfolio.buying_power.toLocaleString()}\n\n`;

                if (positions.length > 0) {
                    message += `🎯 **Active Positions**:\n`;
                    positions.forEach(pos => {
                        message += `• **${pos.symbol}**: ${pos.qty} shares @ $${pos.avg_cost} (${pos.unrealized_pnl >= 0 ? '+' : ''}$${pos.unrealized_pnl})\n`;
                    });
                } else {
                    message += `📝 **No active positions** - Ready for new opportunities!`;
                }

                return { success: true, message, data: { portfolio, positions } };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleEducation(message) {
            try {
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            mode: 'educational',
                            interface: 'atlas_html'
                        }
                    })
                });

                const data = await response.json();

                let responseMessage = `🎓 **Educational Response**\n\n`;
                responseMessage += data.response;

                if (data.book_references) {
                    responseMessage += `\n\n📚 **Related Reading**:\n`;
                    data.book_references.forEach(ref => {
                        responseMessage += `• ${ref.book}: "${ref.quote}"\n`;
                    });
                }

                return { success: true, message: responseMessage, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function handleGeneralChat(message) {
            try {
                const response = await fetch('/api/v1/chat/cot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message
                    })
                });

                const data = await response.json();

                return {
                    success: true,
                    message: data.response || "I understand your message. How can I help you with trading today?",
                    data
                };
            } catch (error) {
                // Fallback to basic chat
                try {
                    const fallbackResponse = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            context: { interface: 'atlas_html' }
                        })
                    });

                    const fallbackData = await fallbackResponse.json();
                    return { success: true, message: fallbackData.response, data: fallbackData };
                } catch (fallbackError) {
                    return { success: false, error: fallbackError.message };
                }
            }
        }

        function displayAdditionalData(data) {
            // This function can be expanded to show charts, tables, etc.
            // For now, we'll just log the data for debugging
            console.log('Additional data:', data);
        }

        // Quick action function
        function quickAction(message) {
            document.getElementById('chatInput').value = message;
            sendMessage();
        }

        // Communication mode switching
        function changeCommunicationMode() {
            const mode = document.getElementById('communicationMode').value;
            const modeDescriptions = {
                'mentor': 'Educational and supportive responses with detailed explanations',
                'professional': 'Direct and analytical responses with minimal emotion',
                'casual': 'Friendly and conversational responses with relatable language',
                'aggressive': 'Confident and action-oriented responses for experienced traders'
            };

            // Send mode change message to A.T.L.A.S
            const message = `Switch to ${mode} communication mode`;
            addMessageToChat('user', `Switching to ${mode} mode`);
            addMessageToChat('assistant', `✅ Communication mode changed to **${mode.toUpperCase()}**!\n\n${modeDescriptions[mode]}\n\nI'll now adapt my responses accordingly. How can I help you trade today?`);
        }

        // Load market data for widget
        async function loadMarketData() {
            try {
                const symbols = ['SPY', 'QQQ'];
                for (const symbol of symbols) {
                    try {
                        const response = await fetch(`/api/v1/quote/${symbol}`);
                        const data = await response.json();

                        const priceElement = document.getElementById(`${symbol.toLowerCase()}Price`);
                        const changeElement = document.getElementById(`${symbol.toLowerCase()}Change`);

                        if (priceElement && changeElement) {
                            priceElement.textContent = `$${data.price}`;
                            const changePercent = data.change_percent;
                            changeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent}%`;
                            changeElement.className = `change ${changePercent >= 0 ? 'positive' : 'negative'}`;
                        }
                    } catch (error) {
                        console.log(`Error loading ${symbol} data:`, error);
                    }
                }
            } catch (error) {
                console.log('Error loading market data:', error);
            }
        }

        // Initialize market data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadMarketData();
            // Refresh market data every 30 seconds
            setInterval(loadMarketData, 30000);
        });

        // Handle Enter key in chat input
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // Profit optimization test function
        async function testProfitOptimization() {
            const accountSize = document.getElementById('accountSize').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '🔄 Generating profit optimization plan...';

            try {
                const response = await fetch('/api/v1/profit-optimization', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        account_size: parseFloat(accountSize),
                        active_positions: [],
                        closed_positions: []
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const allocation = data.optimized_allocation || {};
                    const riskMetrics = data.risk_metrics || {};

                    resultDiv.innerHTML = `✅ SUCCESS! Profit Optimization Plan Generated

🎯 PLAN SUMMARY:
• Account Size: $${parseFloat(accountSize).toLocaleString()}
• Total Allocation: $${(allocation.total_allocation || 0).toLocaleString()}
• Allocation Percentage: ${(allocation.allocation_percentage || 0).toFixed(1)}%
• Diversification: ${allocation.diversification_count || 0} positions

📊 OPPORTUNITIES FOUND:
• Profit Opportunities: ${(data.profit_opportunities || []).length}
• Alpha Signals: ${(data.alpha_signals || []).length}
• Recycling Opportunities: ${(data.recycling_opportunities || []).length}

🛡️ RISK METRICS:
• Max Portfolio Risk: $${(riskMetrics.max_portfolio_risk || 0).toLocaleString()}
• Diversification Score: ${(riskMetrics.diversification_score || 0).toFixed(2)}
• Expected Return: ${((riskMetrics.expected_portfolio_return || 0) * 100).toFixed(1)}%

🎓 EDUCATIONAL INSIGHTS:
${(data.educational_insights || []).map((insight, i) => `${i + 1}. ${insight}`).join('\n')}

🎉 A.T.L.A.S Next-Generation Profit Optimization System is working perfectly!`;
                } else {
                    resultDiv.innerHTML = `❌ Error: ${data.error || 'Unknown error occurred'}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }

        // Trading Chart Functions
        function initializeTradingChart() {
            const canvas = document.getElementById('tradingChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Sample candlestick data
            const candlestickData = [
                {open: 230, high: 235, low: 228, close: 233},
                {open: 233, high: 238, low: 232, close: 236},
                {open: 236, high: 240, low: 234, close: 238},
                {open: 238, high: 242, low: 236, close: 240},
                {open: 240, high: 244, low: 238, close: 241},
                {open: 241, high: 245, low: 239, close: 243},
                {open: 243, high: 247, low: 241, close: 245},
                {open: 245, high: 248, low: 243, close: 246},
                {open: 246, high: 250, low: 244, close: 248},
                {open: 248, high: 252, low: 246, close: 250},
                {open: 250, high: 254, low: 248, close: 252},
                {open: 252, high: 256, low: 250, close: 254},
                {open: 254, high: 258, low: 252, close: 237} // Current price
            ];

            drawCandlestickChart(ctx, candlestickData, canvas.width, canvas.height);
        }

        function drawCandlestickChart(ctx, data, width, height) {
            const padding = 20;
            const chartWidth = width - 2 * padding;
            const chartHeight = height - 2 * padding;

            // Find min and max prices
            let minPrice = Math.min(...data.map(d => d.low));
            let maxPrice = Math.max(...data.map(d => d.high));
            const priceRange = maxPrice - minPrice;

            // Clear canvas
            ctx.fillStyle = 'rgba(0, 20, 40, 0.8)';
            ctx.fillRect(0, 0, width, height);

            // Draw grid
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
            }

            // Draw candlesticks
            const candleWidth = chartWidth / data.length * 0.6;
            const candleSpacing = chartWidth / data.length;

            data.forEach((candle, index) => {
                const x = padding + index * candleSpacing + candleSpacing / 2;
                const openY = padding + (maxPrice - candle.open) / priceRange * chartHeight;
                const closeY = padding + (maxPrice - candle.close) / priceRange * chartHeight;
                const highY = padding + (maxPrice - candle.high) / priceRange * chartHeight;
                const lowY = padding + (maxPrice - candle.low) / priceRange * chartHeight;

                // Determine color
                const isGreen = candle.close > candle.open;
                ctx.fillStyle = isGreen ? '#00ff88' : '#ff4444';
                ctx.strokeStyle = isGreen ? '#00ff88' : '#ff4444';

                // Draw wick
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(x, highY);
                ctx.lineTo(x, lowY);
                ctx.stroke();

                // Draw body
                const bodyTop = Math.min(openY, closeY);
                const bodyHeight = Math.abs(closeY - openY);
                ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
            });
        }

        function initializeVolumeChart() {
            const canvas = document.getElementById('volumeChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // Sample volume data
            const volumeData = [15, 22, 18, 25, 30, 28, 35, 32, 40, 38, 45, 42, 50];

            drawVolumeChart(ctx, volumeData, canvas.width, canvas.height);
        }

        function drawVolumeChart(ctx, data, width, height) {
            const padding = 10;
            const chartWidth = width - 2 * padding;
            const chartHeight = height - 2 * padding;

            const maxVolume = Math.max(...data);
            const barWidth = chartWidth / data.length * 0.8;
            const barSpacing = chartWidth / data.length;

            // Clear canvas
            ctx.fillStyle = 'rgba(0, 10, 20, 0.8)';
            ctx.fillRect(0, 0, width, height);

            // Draw volume bars
            data.forEach((volume, index) => {
                const x = padding + index * barSpacing + barSpacing / 2 - barWidth / 2;
                const barHeight = (volume / maxVolume) * chartHeight;
                const y = height - padding - barHeight;

                ctx.fillStyle = 'rgba(0, 255, 255, 0.6)';
                ctx.fillRect(x, y, barWidth, barHeight);
            });
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                initializeTradingChart();
                initializeVolumeChart();
            }, 100);
        });
    </script>
</body>
</html>
