"""
A.T.L.A.S Orchestrator - Unified System Coordinator
Combines conversational interface, chain-of-thought orchestration, and system coordination
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from config import settings
from models import (
    ChatMessage, AIResponse, Quote, Position, TechnicalIndicators,
    ChainOfThoughtAnalysis, RiskManagementProfile
)

# Import consolidated engines
from atlas_ai_engine import AtlasAIEngine, CommunicationMode
from atlas_trading_engine import AtlasTradingEngine
from atlas_risk_engine import AtlasRiskEngine
from atlas_market_engine import AtlasMarketEngine
from atlas_education_engine import AtlasEducationEngine


class AtlasOrchestrator:
    """
    Master orchestrator that coordinates all A.T.L.A.S components
    Provides unified conversational interface with comprehensive trading intelligence
    """
    
    def __init__(self, mentor_mode: bool = True):
        self.logger = logging.getLogger(__name__)
        
        # Initialize all engines
        self.ai_engine = AtlasAIEngine()
        self.trading_engine = AtlasTradingEngine()
        self.risk_engine = AtlasRiskEngine()
        self.market_engine = AtlasMarketEngine()
        self.education_engine = AtlasEducationEngine()
        
        # Set communication mode
        if mentor_mode:
            self.ai_engine.set_communication_mode(CommunicationMode.MENTOR)
        
        # System state
        self.current_session_id = None
        self.user_profile = self._initialize_user_profile()
        
        self.logger.info("🧠 A.T.L.A.S Orchestrator initialized with all engines")
    
    def _initialize_user_profile(self) -> Dict[str, Any]:
        """Initialize default user profile"""
        return {
            "experience_level": "beginner",
            "risk_tolerance": "conservative",
            "account_size": 10000.0,  # Default paper trading account
            "preferred_strategies": ["ttm_squeeze"],
            "communication_style": "mentor",
            "learning_goals": ["risk_management", "technical_analysis"]
        }
    
    async def start_session(self) -> str:
        """Start new trading session"""
        
        self.current_session_id = self.education_engine.memory_system.start_session()
        
        # Store session start
        self.education_engine.memory_system.store_memory(
            session_id=self.current_session_id,
            memory_type="session",
            content="A.T.L.A.S session started",
            metadata={
                "user_profile": self.user_profile,
                "engines_loaded": ["ai", "trading", "risk", "market", "education"]
            },
            importance_score=0.8
        )
        
        self.logger.info(f"🚀 New A.T.L.A.S session started: {self.current_session_id}")
        return self.current_session_id
    
    async def process_message(self, message: str, session_id: Optional[str] = None) -> AIResponse:
        """
        Process user message with full A.T.L.A.S intelligence pipeline
        """
        
        if not session_id:
            session_id = self.current_session_id or await self.start_session()
        
        try:
            self.logger.info(f"📝 Processing message: {message[:100]}...")
            
            # Store user message
            self.education_engine.memory_system.store_memory(
                session_id=session_id,
                memory_type="conversation",
                content=f"User: {message}",
                metadata={"role": "user"},
                importance_score=0.5
            )
            
            # Classify intent and extract context
            intent = self._classify_message_intent(message)
            context = await self._gather_context(message, intent)
            
            # Route to appropriate processing pipeline
            if intent == "trading_analysis":
                response = await self._process_trading_analysis(message, context, session_id)
            elif intent == "portfolio_request":
                response = await self._process_portfolio_request(message, context, session_id)
            elif intent == "education_request":
                response = await self._process_education_request(message, context, session_id)
            elif intent == "market_scan":
                response = await self._process_market_scan(message, context, session_id)
            elif intent == "risk_assessment":
                response = await self._process_risk_assessment(message, context, session_id)
            else:
                response = await self._process_general_chat(message, context, session_id)
            
            # Store AI response
            self.education_engine.memory_system.store_memory(
                session_id=session_id,
                memory_type="conversation",
                content=f"A.T.L.A.S: {response.response}",
                metadata={
                    "role": "assistant",
                    "intent": intent,
                    "confidence": response.confidence
                },
                importance_score=0.6
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return AIResponse(
                response="I apologize, but I encountered an error processing your request. Please try again or rephrase your question.",
                type="error",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    def _classify_message_intent(self, message: str) -> str:
        """Classify user message intent"""
        
        message_lower = message.lower()
        
        # Trading analysis requests
        if any(word in message_lower for word in ['analyze', 'analysis', 'chart', 'technical', 'buy', 'sell']):
            return "trading_analysis"
        
        # Portfolio requests
        elif any(word in message_lower for word in ['portfolio', 'positions', 'performance', 'profit', 'loss']):
            return "portfolio_request"
        
        # Education requests
        elif any(word in message_lower for word in ['learn', 'explain', 'what is', 'how to', 'teach', 'education']):
            return "education_request"
        
        # Market scanning
        elif any(word in message_lower for word in ['scan', 'opportunities', 'find', 'search', 'screen']):
            return "market_scan"
        
        # Risk assessment
        elif any(word in message_lower for word in ['risk', 'safe', 'dangerous', 'position size']):
            return "risk_assessment"
        
        else:
            return "general_chat"
    
    async def _gather_context(self, message: str, intent: str) -> Dict[str, Any]:
        """Gather relevant context for message processing"""
        
        context = {
            "user_profile": self.user_profile,
            "session_id": self.current_session_id,
            "intent": intent,
            "timestamp": datetime.utcnow()
        }
        
        # Extract symbol if mentioned
        symbol = self._extract_symbol(message)
        if symbol:
            try:
                # Get comprehensive market analysis
                market_analysis = await self.market_engine.get_comprehensive_analysis(symbol)
                context.update({
                    "symbol": symbol,
                    "quote": market_analysis.get("quote"),
                    "technical_indicators": market_analysis.get("technical_indicators"),
                    "news": market_analysis.get("news"),
                    "forecast": market_analysis.get("forecast")
                })
            except Exception as e:
                self.logger.error(f"Error gathering market context for {symbol}: {e}")
        
        return context
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        import re
        
        # Look for stock symbols (1-5 uppercase letters)
        pattern = r'\b[A-Z]{1,5}\b'
        matches = re.findall(pattern, message.upper())
        
        # Filter out common words
        common_words = {'THE', 'AND', 'OR', 'BUT', 'FOR', 'AT', 'TO', 'FROM', 'UP', 'DOWN', 'IN', 'OUT', 'ON', 'OFF'}
        symbols = [match for match in matches if match not in common_words and len(match) <= 5]
        
        return symbols[0] if symbols else None
    
    async def _process_trading_analysis(self, message: str, context: Dict[str, Any], 
                                      session_id: str) -> AIResponse:
        """Process trading analysis request"""
        
        symbol = context.get("symbol")
        if not symbol:
            return AIResponse(
                response="I'd be happy to analyze a stock for you! Please specify which symbol you'd like me to analyze (e.g., 'analyze AAPL' or 'what do you think about TSLA?').",
                type="trading_analysis",
                confidence=0.8,
                timestamp=datetime.utcnow()
            )
        
        try:
            # Get comprehensive analysis
            quote = Quote(**context["quote"]) if context.get("quote") else None
            indicators = TechnicalIndicators(**context["technical_indicators"]) if context.get("technical_indicators") else None
            
            if not quote or not indicators:
                return AIResponse(
                    response=f"I'm having trouble getting market data for {symbol}. Please try again in a moment.",
                    type="trading_analysis",
                    confidence=0.2,
                    timestamp=datetime.utcnow()
                )
            
            # Perform risk assessment
            risk_assessment = self.risk_engine.comprehensive_risk_assessment(
                symbol=symbol,
                entry_price=quote.price,
                stop_loss=quote.price * 0.97,  # 3% stop loss
                account_size=self.user_profile["account_size"],
                confidence_score=0.7,  # Default confidence
                current_positions=[],  # Would get from trading engine
                market_conditions={"vix": 20}  # Would get from market engine
            )
            
            # Generate AI response with full context
            ai_response = await self.ai_engine.process_trading_query(message, context)
            
            # Enhance response with risk assessment
            enhanced_response = ai_response.response + f"\n\n🛡️ **Risk Assessment:**\n{risk_assessment['final_recommendation']}"
            
            if risk_assessment.get("position_sizing"):
                pos_size = risk_assessment["position_sizing"]
                enhanced_response += f"\n\n📊 **Position Sizing:**\n• Recommended shares: {pos_size['recommended_shares']}\n• Risk amount: ${pos_size['risk_amount']:.2f}\n• Risk percentage: {pos_size['risk_percent']:.1f}%"
            
            return AIResponse(
                response=enhanced_response,
                type="trading_analysis",
                confidence=ai_response.confidence,
                requires_action=True,
                trading_plan=risk_assessment,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error in trading analysis: {e}")
            return AIResponse(
                response=f"I encountered an error analyzing {symbol}. Please try again.",
                type="trading_analysis",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _process_portfolio_request(self, message: str, context: Dict[str, Any], 
                                       session_id: str) -> AIResponse:
        """Process portfolio status request"""
        
        try:
            # Get portfolio status
            portfolio_status = await self.trading_engine.get_portfolio_status()
            
            response_text = "📊 **Portfolio Status:**\n\n"
            
            portfolio = portfolio_status.get("portfolio", {})
            response_text += f"• Total Value: ${portfolio.get('total_value', 0):,.2f}\n"
            response_text += f"• Cash: ${portfolio.get('cash', 0):,.2f}\n"
            response_text += f"• Day P&L: ${portfolio.get('day_pnl', 0):,.2f}\n"
            response_text += f"• Total P&L: ${portfolio.get('total_pnl', 0):,.2f}\n"
            response_text += f"• Active Positions: {portfolio.get('active_trades', 0)}\n"
            response_text += f"• Win Rate: {portfolio.get('win_rate', 0)*100:.1f}%\n\n"
            
            positions = portfolio_status.get("positions", [])
            if positions:
                response_text += "📈 **Current Positions:**\n"
                for pos in positions[:5]:  # Show top 5
                    response_text += f"• {pos['symbol']}: {pos['quantity']} shares, P&L: ${pos['unrealized_pl']:.2f}\n"
            else:
                response_text += "No current positions.\n\n"
            
            response_text += "\n💡 This is your paper trading portfolio for educational purposes."
            
            return AIResponse(
                response=response_text,
                type="portfolio_request",
                confidence=0.9,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio status: {e}")
            return AIResponse(
                response="I'm having trouble accessing your portfolio information. Please try again.",
                type="portfolio_request",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _process_education_request(self, message: str, context: Dict[str, Any], 
                                       session_id: str) -> AIResponse:
        """Process educational request"""
        
        try:
            # Process with education engine
            education_result = self.education_engine.process_educational_query(
                query=message,
                session_id=session_id,
                user_level=self.user_profile["experience_level"]
            )
            
            response_text = education_result["response"]
            
            # Add learning progress if available
            if education_result.get("related_concepts"):
                response_text += f"\n\n🎯 **Related Concepts:** {', '.join(education_result['related_concepts'])}"
            
            return AIResponse(
                response=response_text,
                type="education_request",
                confidence=education_result.get("confidence", 0.8),
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error processing education request: {e}")
            return AIResponse(
                response="I encountered an error while processing your educational question. Please try rephrasing it.",
                type="education_request",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _process_market_scan(self, message: str, context: Dict[str, Any], 
                                 session_id: str) -> AIResponse:
        """Process market scanning request"""
        
        try:
            # Scan market opportunities
            scan_results = await self.market_engine.scan_market_opportunities()
            
            response_text = "🔍 **Market Scan Results:**\n\n"
            
            ttm_opportunities = scan_results.get("ttm_squeeze_opportunities", [])
            if ttm_opportunities:
                response_text += "🎯 **TTM Squeeze Opportunities:**\n"
                for opp in ttm_opportunities[:5]:
                    response_text += f"• {opp['symbol']}: {opp['signal_strength']*100:.0f}% confidence - {opp['reasoning']}\n"
                response_text += "\n"
            
            market_sentiment = scan_results.get("market_sentiment", {})
            if market_sentiment:
                sentiment = market_sentiment.get("overall_sentiment", "neutral")
                response_text += f"📊 **Market Sentiment:** {sentiment.title()}\n\n"
            
            response_text += f"Total opportunities found: {scan_results.get('total_opportunities', 0)}\n\n"
            response_text += "💡 Remember: These are educational signals for paper trading practice."
            
            return AIResponse(
                response=response_text,
                type="market_scan",
                confidence=0.8,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error in market scan: {e}")
            return AIResponse(
                response="I encountered an error while scanning the market. Please try again.",
                type="market_scan",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _process_risk_assessment(self, message: str, context: Dict[str, Any], 
                                     session_id: str) -> AIResponse:
        """Process risk assessment request"""
        
        symbol = context.get("symbol")
        if not symbol:
            return AIResponse(
                response="To assess risk, please specify a symbol (e.g., 'What's the risk of buying AAPL?').",
                type="risk_assessment",
                confidence=0.8,
                timestamp=datetime.utcnow()
            )
        
        try:
            quote = Quote(**context["quote"]) if context.get("quote") else None
            if not quote:
                return AIResponse(
                    response=f"I'm having trouble getting market data for {symbol} to assess risk.",
                    type="risk_assessment",
                    confidence=0.2,
                    timestamp=datetime.utcnow()
                )
            
            # Perform comprehensive risk assessment
            risk_assessment = self.risk_engine.comprehensive_risk_assessment(
                symbol=symbol,
                entry_price=quote.price,
                stop_loss=quote.price * 0.97,
                account_size=self.user_profile["account_size"],
                confidence_score=0.7,
                current_positions=[],
                market_conditions={"vix": 20}
            )
            
            response_text = f"🛡️ **Risk Assessment for {symbol}:**\n\n"
            response_text += f"**Final Recommendation:** {risk_assessment['final_recommendation']}\n\n"
            
            if risk_assessment.get("safety"):
                safety = risk_assessment["safety"]
                response_text += f"**Overall Risk Level:** {safety['overall_risk'].title()}\n"
                
                if safety["risk_factors"]:
                    response_text += f"**Risk Factors:**\n"
                    for factor in safety["risk_factors"][:3]:
                        response_text += f"• {factor}\n"
                
                if safety["educational_warnings"]:
                    response_text += f"\n**Educational Notes:**\n"
                    for warning in safety["educational_warnings"][:2]:
                        response_text += f"• {warning}\n"
            
            return AIResponse(
                response=response_text,
                type="risk_assessment",
                confidence=0.9,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment: {e}")
            return AIResponse(
                response=f"I encountered an error assessing risk for {symbol}. Please try again.",
                type="risk_assessment",
                confidence=0.0,
                timestamp=datetime.utcnow()
            )
    
    async def _process_general_chat(self, message: str, context: Dict[str, Any], 
                                  session_id: str) -> AIResponse:
        """Process general chat message"""
        
        # Use AI engine for general conversation
        return await self.ai_engine.process_trading_query(message, context)
    
    def set_user_profile(self, profile_updates: Dict[str, Any]):
        """Update user profile"""
        self.user_profile.update(profile_updates)
        
        # Update AI communication style if changed
        if "communication_style" in profile_updates:
            style = profile_updates["communication_style"]
            if style == "mentor":
                self.ai_engine.set_communication_mode(CommunicationMode.MENTOR)
            elif style == "professional":
                self.ai_engine.set_communication_mode(CommunicationMode.PROFESSIONAL)
            elif style == "casual":
                self.ai_engine.set_communication_mode(CommunicationMode.CASUAL)
            elif style == "aggressive":
                self.ai_engine.set_communication_mode(CommunicationMode.AGGRESSIVE)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        
        try:
            # Get portfolio status
            portfolio_status = await self.trading_engine.get_portfolio_status()
            
            # Get learning progress
            learning_summary = self.education_engine.get_learning_summary(self.current_session_id or "default")
            
            # Get market scan
            market_scan = await self.market_engine.scan_market_opportunities()
            
            return {
                "session_id": self.current_session_id,
                "user_profile": self.user_profile,
                "portfolio": portfolio_status,
                "learning_progress": learning_summary,
                "market_opportunities": len(market_scan.get("ttm_squeeze_opportunities", [])),
                "system_health": "operational",
                "engines_status": {
                    "ai_engine": "active",
                    "trading_engine": "active",
                    "risk_engine": "active",
                    "market_engine": "active",
                    "education_engine": "active"
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {
                "error": str(e),
                "system_health": "degraded",
                "timestamp": datetime.utcnow().isoformat()
            }
