"""
A.T.L.A.S Risk Engine - Consolidated Risk Management and Safety System
Combines risk management, safety guardrails, validation, and advanced risk controls
"""

import logging
import math
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum

from config import settings
from models import (
    Position, Quote, TechnicalIndicators, ChainOfThoughtAnalysis,
    RiskManagementProfile
)


class RiskLevel(Enum):
    """Risk assessment levels"""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    EXTREME = "extreme"


class SafetyFlag(Enum):
    """Safety validation flags"""
    POSITION_SIZE_WARNING = "position_size_warning"
    CORRELATION_WARNING = "correlation_warning"
    VOLATILITY_WARNING = "volatility_warning"
    CONFIDENCE_WARNING = "confidence_warning"
    MARKET_CONDITIONS_WARNING = "market_conditions_warning"
    PAPER_TRADING_REQUIRED = "paper_trading_required"


@dataclass
class PositionSizeCalculation:
    """Position sizing calculation result"""
    recommended_shares: int
    dollar_amount: float
    risk_amount: float
    risk_percent: float
    kelly_fraction: float
    confidence_adjustment: float
    reasoning: str
    warnings: List[str]


@dataclass
class PreTradeValidation:
    """Pre-trade validation result"""
    is_valid: bool
    risk_score: float  # 0.0 to 1.0
    confidence_score: float
    warnings: List[str]
    blockers: List[str]
    educational_notes: List[str]
    recommendation: str
    position_size_adjustment: float


@dataclass
class SafetyAssessment:
    """Comprehensive safety assessment"""
    overall_risk: RiskLevel
    safety_flags: List[SafetyFlag]
    risk_factors: List[str]
    mitigation_strategies: List[str]
    educational_warnings: List[str]
    is_safe_to_proceed: bool
    recommended_adjustments: Dict[str, Any]


class KellyCriterionCalculator:
    """Kelly Criterion position sizing calculator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_kelly_fraction(self, win_probability: float, 
                                avg_win: float, avg_loss: float) -> float:
        """Calculate Kelly Criterion fraction"""
        
        if avg_loss <= 0 or win_probability <= 0 or win_probability >= 1:
            return 0.0
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_probability, q = 1-p
        b = avg_win / abs(avg_loss)
        p = win_probability
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # Cap Kelly fraction at 25% for safety
        return min(max(kelly_fraction, 0.0), 0.25)
    
    def calculate_position_size(self, account_size: float, kelly_fraction: float,
                              confidence_score: float, entry_price: float,
                              stop_loss: float) -> PositionSizeCalculation:
        """Calculate optimal position size using Kelly Criterion"""
        
        # Adjust Kelly fraction based on confidence
        confidence_adjustment = confidence_score * 0.8 + 0.2  # Scale 0.2-1.0
        adjusted_kelly = kelly_fraction * confidence_adjustment
        
        # Calculate risk amount
        risk_per_share = abs(entry_price - stop_loss)
        risk_percent = risk_per_share / entry_price
        
        # Position size based on Kelly and risk management
        max_risk_amount = account_size * settings.DEFAULT_RISK_PERCENT / 100
        kelly_risk_amount = account_size * adjusted_kelly
        
        # Use the smaller of Kelly or max risk
        risk_amount = min(kelly_risk_amount, max_risk_amount)
        
        # Calculate shares
        if risk_per_share > 0:
            recommended_shares = int(risk_amount / risk_per_share)
        else:
            recommended_shares = 0
        
        dollar_amount = recommended_shares * entry_price
        actual_risk_percent = (risk_amount / account_size) * 100
        
        warnings = []
        if adjusted_kelly < kelly_fraction:
            warnings.append(f"Position size reduced due to confidence level ({confidence_score*100:.0f}%)")
        
        if dollar_amount > account_size * 0.15:
            warnings.append("Large position size - consider reducing for better diversification")
        
        reasoning = f"Kelly fraction: {kelly_fraction:.3f}, Confidence adjusted: {adjusted_kelly:.3f}, Risk per share: ${risk_per_share:.2f}"
        
        return PositionSizeCalculation(
            recommended_shares=recommended_shares,
            dollar_amount=dollar_amount,
            risk_amount=risk_amount,
            risk_percent=actual_risk_percent,
            kelly_fraction=adjusted_kelly,
            confidence_adjustment=confidence_adjustment,
            reasoning=reasoning,
            warnings=warnings
        )


class RiskManagementEngine:
    """Core risk management with Kelly Criterion and safety checks"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.kelly_calculator = KellyCriterionCalculator()
        
        # Historical performance for Kelly calculation (would be dynamic in production)
        self.historical_win_rate = 0.65
        self.historical_avg_win = 0.08  # 8% average win
        self.historical_avg_loss = 0.04  # 4% average loss
    
    def calculate_position_size(self, symbol: str, entry_price: float,
                              stop_loss: float, account_size: float,
                              confidence_score: float) -> PositionSizeCalculation:
        """Calculate optimal position size"""
        
        # Calculate Kelly fraction
        kelly_fraction = self.kelly_calculator.calculate_kelly_fraction(
            self.historical_win_rate,
            self.historical_avg_win,
            self.historical_avg_loss
        )
        
        # Calculate position size
        return self.kelly_calculator.calculate_position_size(
            account_size, kelly_fraction, confidence_score, entry_price, stop_loss
        )
    
    def validate_trade_setup(self, symbol: str, entry_price: float,
                           position_size: PositionSizeCalculation,
                           current_positions: List[Position],
                           cot_analysis: Optional[ChainOfThoughtAnalysis] = None,
                           market_conditions: Optional[Dict[str, Any]] = None) -> PreTradeValidation:
        """Comprehensive pre-trade validation"""
        
        warnings = []
        blockers = []
        educational_notes = []
        risk_score = 1.0
        
        # Get account size from position size calculation
        account_size = position_size.dollar_amount / (position_size.risk_percent / 100) if position_size.risk_percent > 0 else 100000
        
        # Confidence validation
        confidence_score = cot_analysis.final_confidence if cot_analysis else 0.5
        if confidence_score < 0.70:
            warnings.append(f"Low confidence signal ({confidence_score*100:.0f}%) - consider reducing position size")
            educational_notes.append("💡 High-confidence trades (70%+) have better success rates historically")
            risk_score -= 0.2
        
        # Market conditions check
        if market_conditions and market_conditions.get('vix', 0) > 30:
            warnings.append("High market volatility (VIX > 30) - increased risk")
            educational_notes.append("📊 High VIX indicates market fear - consider smaller positions")
            risk_score -= 0.15
        
        # Position size validation
        position_percent = (position_size.dollar_amount / account_size) * 100
        if position_percent > 15:
            warnings.append(f"Large position size: {position_percent:.1f}% of account")
            educational_notes.append("⚠️ Large positions increase risk - consider diversifying across multiple trades")
            risk_score -= 0.2
        
        # Sector diversification check
        sector_exposure = self._estimate_sector_exposure(symbol, current_positions)
        if sector_exposure > 0.4:  # 40% sector limit
            warnings.append(f"High sector concentration: {sector_exposure*100:.0f}%")
            educational_notes.append("🏭 Diversify across sectors to reduce correlation risk")
            risk_score -= 0.2
        
        # Daily loss limit check
        current_day_loss = self._calculate_current_day_loss(current_positions)
        daily_loss_limit = account_size * 0.03  # 3% daily limit
        
        if current_day_loss + position_size.risk_amount > daily_loss_limit:
            blockers.append("Trade would exceed daily loss limit (3%)")
            educational_notes.append("🛡️ Daily loss limits protect your account from major drawdowns")
        
        # Paper trading enforcement
        if not settings.PAPER_TRADING:
            blockers.append("Real trading not enabled - paper trading required for safety")
            educational_notes.append("📝 Paper trading helps you learn without risking real money")
        
        # Risk/reward ratio check
        if position_size.risk_percent > 0:
            risk_reward = abs(entry_price - stop_loss) / entry_price
            if risk_reward > 0.05:  # 5% risk threshold
                warnings.append(f"High risk per trade: {risk_reward*100:.1f}%")
                educational_notes.append("⚖️ Keep risk per trade under 2-3% for long-term success")
                risk_score -= 0.1
        
        # Generate final recommendation
        is_valid = len(blockers) == 0
        
        if is_valid and confidence_score >= 0.80 and risk_score >= 0.8:
            recommendation = "STRONG GO - Excellent setup with high confidence and low risk"
        elif is_valid and confidence_score >= 0.65 and risk_score >= 0.6:
            recommendation = "GO - Good setup, proceed with recommended position size"
        elif is_valid and len(warnings) <= 2:
            recommendation = "CAUTION - Proceed with reduced position size"
        elif is_valid:
            recommendation = "HIGH CAUTION - Multiple warnings, consider waiting"
        else:
            recommendation = "NO GO - Critical issues must be resolved first"
        
        # Position size adjustment based on risk factors
        adjustment_factor = 1.0
        if len(warnings) > 0:
            adjustment_factor = max(0.5, 1.0 - (len(warnings) * 0.15))
        
        return PreTradeValidation(
            is_valid=is_valid,
            risk_score=risk_score,
            confidence_score=confidence_score,
            warnings=warnings,
            blockers=blockers,
            educational_notes=educational_notes,
            recommendation=recommendation,
            position_size_adjustment=adjustment_factor
        )
    
    def _estimate_sector_exposure(self, symbol: str, positions: List[Position]) -> float:
        """Estimate sector exposure (simplified)"""
        # Simplified sector mapping
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META']
        
        if symbol in tech_symbols:
            tech_exposure = sum(
                float(pos.market_value) for pos in positions 
                if pos.symbol in tech_symbols
            )
            total_value = sum(float(pos.market_value) for pos in positions)
            return tech_exposure / total_value if total_value > 0 else 0.0
        
        return 0.0  # Assume low exposure for other sectors
    
    def _calculate_current_day_loss(self, positions: List[Position]) -> float:
        """Calculate current day unrealized losses"""
        day_loss = sum(
            float(pos.unrealized_pl) for pos in positions 
            if float(pos.unrealized_pl) < 0
        )
        return abs(day_loss)


class SafetyGuardrailsEngine:
    """Comprehensive safety measures and guardrails"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.vix_circuit_breaker = 40.0  # VIX level that triggers circuit breaker
        self.max_daily_trades = 10
        self.max_position_concentration = 0.20  # 20% max per position
    
    def assess_safety(self, symbol: str, position_size: PositionSizeCalculation,
                     current_positions: List[Position],
                     market_conditions: Dict[str, Any],
                     user_experience_level: str = "beginner") -> SafetyAssessment:
        """Comprehensive safety assessment"""
        
        safety_flags = []
        risk_factors = []
        mitigation_strategies = []
        educational_warnings = []
        
        # VIX circuit breaker
        vix_level = market_conditions.get('vix', 20)
        if vix_level > self.vix_circuit_breaker:
            safety_flags.append(SafetyFlag.VOLATILITY_WARNING)
            risk_factors.append(f"Extreme market volatility (VIX: {vix_level})")
            mitigation_strategies.append("Wait for market volatility to decrease")
            educational_warnings.append("🌪️ High VIX indicates market fear - it's often better to wait")
        
        # Position size safety
        account_value = sum(float(pos.market_value) for pos in current_positions)
        if account_value > 0:
            position_percent = position_size.dollar_amount / account_value
            if position_percent > self.max_position_concentration:
                safety_flags.append(SafetyFlag.POSITION_SIZE_WARNING)
                risk_factors.append(f"Position size too large: {position_percent*100:.1f}%")
                mitigation_strategies.append("Reduce position size to under 15% of account")
        
        # Correlation check
        correlation_risk = self._assess_correlation_risk(symbol, current_positions)
        if correlation_risk > 0.7:
            safety_flags.append(SafetyFlag.CORRELATION_WARNING)
            risk_factors.append("High correlation with existing positions")
            mitigation_strategies.append("Diversify across different sectors/asset classes")
        
        # Paper trading enforcement
        if not settings.PAPER_TRADING:
            safety_flags.append(SafetyFlag.PAPER_TRADING_REQUIRED)
            risk_factors.append("Real trading not recommended for learning")
            mitigation_strategies.append("Use paper trading to practice without risk")
            educational_warnings.append("📝 Paper trading is the safest way to learn")
        
        # Experience level adjustments
        if user_experience_level == "beginner":
            educational_warnings.extend([
                "🎓 Start with small positions to learn",
                "📚 Focus on education over profits initially",
                "⏰ Take time to understand each trade setup"
            ])
        
        # Determine overall risk level
        if len(safety_flags) == 0:
            overall_risk = RiskLevel.LOW
        elif len(safety_flags) <= 2:
            overall_risk = RiskLevel.MODERATE
        elif len(safety_flags) <= 4:
            overall_risk = RiskLevel.HIGH
        else:
            overall_risk = RiskLevel.EXTREME
        
        # Safety decision
        is_safe = overall_risk in [RiskLevel.LOW, RiskLevel.MODERATE]
        
        # Recommended adjustments
        recommended_adjustments = {}
        if SafetyFlag.POSITION_SIZE_WARNING in safety_flags:
            recommended_adjustments["position_size_multiplier"] = 0.5
        if SafetyFlag.VOLATILITY_WARNING in safety_flags:
            recommended_adjustments["confidence_threshold"] = 0.8
        
        return SafetyAssessment(
            overall_risk=overall_risk,
            safety_flags=safety_flags,
            risk_factors=risk_factors,
            mitigation_strategies=mitigation_strategies,
            educational_warnings=educational_warnings,
            is_safe_to_proceed=is_safe,
            recommended_adjustments=recommended_adjustments
        )
    
    def _assess_correlation_risk(self, symbol: str, positions: List[Position]) -> float:
        """Assess correlation risk with existing positions"""
        # Simplified correlation assessment
        # In production, this would use actual correlation data
        
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META']
        
        if symbol in tech_symbols:
            tech_positions = [pos for pos in positions if pos.symbol in tech_symbols]
            if len(tech_positions) >= 3:
                return 0.8  # High correlation
            elif len(tech_positions) >= 1:
                return 0.5  # Moderate correlation
        
        return 0.2  # Low correlation


class AdvancedRiskControls:
    """Advanced risk control mechanisms"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.risk_limits = {
            "max_daily_loss_percent": 3.0,
            "max_position_percent": 15.0,
            "max_sector_percent": 40.0,
            "min_confidence_threshold": 0.65,
            "max_correlation": 0.85
        }
    
    def validate_risk_limits(self, trade_request: Dict[str, Any],
                           current_portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trade against all risk limits"""
        
        violations = []
        warnings = []
        
        # Daily loss limit check
        current_day_loss = current_portfolio.get('day_pnl', 0)
        potential_loss = trade_request.get('risk_amount', 0)
        account_size = current_portfolio.get('total_value', 100000)
        
        total_potential_loss = abs(current_day_loss) + potential_loss
        loss_percent = (total_potential_loss / account_size) * 100
        
        if loss_percent > self.risk_limits["max_daily_loss_percent"]:
            violations.append(f"Would exceed daily loss limit: {loss_percent:.1f}% > {self.risk_limits['max_daily_loss_percent']}%")
        
        # Position size limit
        position_percent = (trade_request.get('position_value', 0) / account_size) * 100
        if position_percent > self.risk_limits["max_position_percent"]:
            warnings.append(f"Large position size: {position_percent:.1f}%")
        
        # Confidence threshold
        confidence = trade_request.get('confidence', 0.5)
        if confidence < self.risk_limits["min_confidence_threshold"]:
            warnings.append(f"Low confidence: {confidence*100:.0f}%")
        
        return {
            "is_valid": len(violations) == 0,
            "violations": violations,
            "warnings": warnings,
            "risk_score": max(0.0, 1.0 - len(violations) * 0.5 - len(warnings) * 0.1)
        }


class AtlasRiskEngine:
    """Main risk engine coordinating all risk management functionality"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.risk_manager = RiskManagementEngine()
        self.safety_engine = SafetyGuardrailsEngine()
        self.advanced_controls = AdvancedRiskControls()
    
    def comprehensive_risk_assessment(self, symbol: str, entry_price: float,
                                    stop_loss: float, account_size: float,
                                    confidence_score: float,
                                    current_positions: List[Position],
                                    market_conditions: Dict[str, Any],
                                    cot_analysis: Optional[ChainOfThoughtAnalysis] = None) -> Dict[str, Any]:
        """Perform comprehensive risk assessment"""
        
        try:
            # 1. Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                symbol, entry_price, stop_loss, account_size, confidence_score
            )
            
            # 2. Validate trade setup
            validation = self.risk_manager.validate_trade_setup(
                symbol, entry_price, position_size, current_positions, cot_analysis, market_conditions
            )
            
            # 3. Safety assessment
            safety = self.safety_engine.assess_safety(
                symbol, position_size, current_positions, market_conditions
            )
            
            # 4. Advanced risk controls
            trade_request = {
                "symbol": symbol,
                "position_value": position_size.dollar_amount,
                "risk_amount": position_size.risk_amount,
                "confidence": confidence_score
            }
            
            portfolio_data = {
                "total_value": account_size,
                "day_pnl": sum(float(pos.unrealized_pl) for pos in current_positions)
            }
            
            risk_limits = self.advanced_controls.validate_risk_limits(trade_request, portfolio_data)
            
            # 5. Generate final recommendation
            final_recommendation = self._generate_final_recommendation(
                validation, safety, risk_limits, position_size
            )
            
            return {
                "position_sizing": {
                    "recommended_shares": position_size.recommended_shares,
                    "dollar_amount": position_size.dollar_amount,
                    "risk_amount": position_size.risk_amount,
                    "risk_percent": position_size.risk_percent,
                    "kelly_fraction": position_size.kelly_fraction,
                    "reasoning": position_size.reasoning,
                    "warnings": position_size.warnings
                },
                "validation": {
                    "is_valid": validation.is_valid,
                    "risk_score": validation.risk_score,
                    "confidence_score": validation.confidence_score,
                    "warnings": validation.warnings,
                    "blockers": validation.blockers,
                    "educational_notes": validation.educational_notes,
                    "recommendation": validation.recommendation
                },
                "safety": {
                    "overall_risk": safety.overall_risk.value,
                    "safety_flags": [flag.value for flag in safety.safety_flags],
                    "risk_factors": safety.risk_factors,
                    "mitigation_strategies": safety.mitigation_strategies,
                    "educational_warnings": safety.educational_warnings,
                    "is_safe": safety.is_safe_to_proceed
                },
                "risk_limits": risk_limits,
                "final_recommendation": final_recommendation
            }
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive risk assessment: {e}")
            return {
                "error": str(e),
                "final_recommendation": "ERROR - Unable to assess risk, do not proceed"
            }
    
    def _generate_final_recommendation(self, validation: PreTradeValidation,
                                     safety: SafetyAssessment,
                                     risk_limits: Dict[str, Any],
                                     position_size: PositionSizeCalculation) -> str:
        """Generate final trading recommendation"""
        
        if not validation.is_valid or not safety.is_safe_to_proceed or not risk_limits["is_valid"]:
            return "❌ DO NOT TRADE - Critical risk factors identified"
        
        if safety.overall_risk == RiskLevel.LOW and validation.risk_score > 0.8:
            return f"✅ PROCEED - Low risk setup, recommended size: {position_size.recommended_shares} shares"
        
        if safety.overall_risk == RiskLevel.MODERATE and validation.risk_score > 0.6:
            adjusted_shares = int(position_size.recommended_shares * 0.7)
            return f"⚠️ PROCEED WITH CAUTION - Reduce position to {adjusted_shares} shares"
        
        return "🛑 WAIT - Risk factors too high, look for better opportunity"
