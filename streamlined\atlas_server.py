"""
A.T.L.A.S AI Trading System - Streamlined Server
Consolidated FastAPI server with unified orchestrator architecture
"""

import asyncio
import logging
import logging.config
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel

# Import consolidated modules
from config import settings, LOGGING_CONFIG
from models import ChatMessage, AIResponse
from atlas_orchestrator import AtlasOrchestrator

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System - Streamlined",
    description="Advanced Trading & Learning Analysis System with Consolidated Architecture",
    version="3.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class QuoteRequest(BaseModel):
    symbol: str

class AnalysisRequest(BaseModel):
    symbol: str
    timeframe: str = "1day"

class UserProfileUpdate(BaseModel):
    experience_level: Optional[str] = None
    risk_tolerance: Optional[str] = None
    account_size: Optional[float] = None
    communication_style: Optional[str] = None

# Global A.T.L.A.S Orchestrator
atlas_orchestrator = AtlasOrchestrator(mentor_mode=True)

@app.on_event("startup")
async def startup_event():
    """Initialize A.T.L.A.S system on startup"""
    logger.info("🚀 Starting A.T.L.A.S AI Trading System - Streamlined Architecture")
    logger.info("🎯 Advanced Trading & Learning Analysis System")
    logger.info("🧠 Consolidated AI Engine Architecture")
    
    # Start A.T.L.A.S session
    session_id = await atlas_orchestrator.start_session()
    logger.info(f"✅ A.T.L.A.S system initialized with session: {session_id}")

@app.get("/")
async def root():
    """Root endpoint - serve the main interface"""
    return FileResponse("atlas_interface.html")

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    try:
        system_status = await atlas_orchestrator.get_system_status()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "3.0.0",
            "architecture": "consolidated",
            "system_status": system_status,
            "engines": {
                "ai_engine": "active",
                "trading_engine": "active", 
                "risk_engine": "active",
                "market_engine": "active",
                "education_engine": "active"
            },
            "features": {
                "paper_trading": True,
                "real_time_data": True,
                "ai_analysis": True,
                "risk_management": True,
                "chain_of_thought": True,
                "multi_agent_analysis": True,
                "profit_optimization": True,
                "educational_rag": True,
                "conversational_interface": True
            }
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "degraded",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.post("/api/v1/chat")
async def chat_endpoint(request: ChatRequest) -> AIResponse:
    """Main conversational interface endpoint"""
    try:
        logger.info(f"💬 Processing chat message: {request.message[:100]}...")
        
        # Process message through A.T.L.A.S orchestrator
        response = await atlas_orchestrator.process_message(
            message=request.message,
            session_id=request.session_id
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return AIResponse(
            response="I apologize, but I encountered an error processing your request. Please try again.",
            type="error",
            confidence=0.0,
            timestamp=datetime.utcnow()
        )

@app.get("/api/v1/quote/{symbol}")
async def get_quote(symbol: str):
    """Get real-time quote through market engine"""
    try:
        # Get comprehensive analysis including quote
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        return analysis["quote"]
        
    except Exception as e:
        logger.error(f"Quote endpoint error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis")
async def get_analysis(request: AnalysisRequest):
    """Get comprehensive market analysis"""
    try:
        # Get comprehensive analysis through market engine
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(request.symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        return analysis
        
    except Exception as e:
        logger.error(f"Analysis endpoint error for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/portfolio")
async def get_portfolio():
    """Get portfolio status"""
    try:
        portfolio_status = await atlas_orchestrator.trading_engine.get_portfolio_status()
        return portfolio_status
        
    except Exception as e:
        logger.error(f"Portfolio endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/scan")
async def market_scan():
    """Scan market for opportunities"""
    try:
        scan_results = await atlas_orchestrator.market_engine.scan_market_opportunities()
        return scan_results
        
    except Exception as e:
        logger.error(f"Market scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/risk-assessment")
async def risk_assessment(request: AnalysisRequest):
    """Get risk assessment for symbol"""
    try:
        # Get quote first
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(request.symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        quote = analysis["quote"]
        
        # Perform risk assessment
        risk_result = atlas_orchestrator.risk_engine.comprehensive_risk_assessment(
            symbol=request.symbol,
            entry_price=quote["price"],
            stop_loss=quote["price"] * 0.97,  # 3% stop loss
            account_size=atlas_orchestrator.user_profile["account_size"],
            confidence_score=0.7,
            current_positions=[],
            market_conditions={"vix": 20}
        )
        
        return risk_result
        
    except Exception as e:
        logger.error(f"Risk assessment error for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/education")
async def education_query(request: ChatRequest):
    """Educational query endpoint"""
    try:
        session_id = request.session_id or atlas_orchestrator.current_session_id
        
        education_result = atlas_orchestrator.education_engine.process_educational_query(
            query=request.message,
            session_id=session_id,
            user_level=atlas_orchestrator.user_profile["experience_level"]
        )
        
        return education_result
        
    except Exception as e:
        logger.error(f"Education query error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/learning-progress")
async def get_learning_progress(session_id: Optional[str] = None):
    """Get learning progress summary"""
    try:
        session_id = session_id or atlas_orchestrator.current_session_id or "default"
        
        learning_summary = atlas_orchestrator.education_engine.get_learning_summary(session_id)
        return learning_summary
        
    except Exception as e:
        logger.error(f"Learning progress error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/profile")
async def update_user_profile(profile: UserProfileUpdate):
    """Update user profile"""
    try:
        # Convert to dict and filter None values
        profile_updates = {k: v for k, v in profile.dict().items() if v is not None}
        
        # Update profile
        atlas_orchestrator.set_user_profile(profile_updates)
        
        return {
            "status": "success",
            "updated_profile": atlas_orchestrator.user_profile,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/system-status")
async def get_system_status():
    """Get comprehensive system status"""
    try:
        system_status = await atlas_orchestrator.get_system_status()
        return system_status
        
    except Exception as e:
        logger.error(f"System status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/profit-optimization")
async def profit_optimization():
    """Execute profit optimization strategy"""
    try:
        account_size = atlas_orchestrator.user_profile["account_size"]
        
        optimization_result = await atlas_orchestrator.trading_engine.execute_profit_optimization_strategy(account_size)
        
        return optimization_result
        
    except Exception as e:
        logger.error(f"Profit optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Endpoint not found", "detail": "Please check the API documentation"}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal server error", "detail": "Please try again later"}

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting Streamlined A.T.L.A.S AI Server...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("🧠 Consolidated Architecture with Unified Orchestrator")
    
    uvicorn.run(
        "atlas_server:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
