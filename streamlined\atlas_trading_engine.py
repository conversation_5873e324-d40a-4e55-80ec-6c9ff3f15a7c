"""
A.T.L.A.S Trading Engine - Consolidated Trading and Execution System
Combines trading execution, profit optimization, strategy management, and monitoring
"""

import asyncio
import logging
import alpaca_trade_api as tradeapi
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass

from config import settings
from models import (
    Quote, Position, OrderRequest, TradingSignal, ScanResult,
    TechnicalIndicators, ChainOfThoughtAnalysis
)


class StrategyTier(Enum):
    """Multi-tier strategy framework"""
    TIER_1_MOMENTUM = "tier_1_momentum"
    TIER_2_OPTIONS = "tier_2_options"
    TIER_3_EARNINGS = "tier_3_earnings"
    TIER_4_SCALPING = "tier_4_scalping"
    TIER_5_SQUEEZE = "tier_5_squeeze"


class OrderStatus(Enum):
    """Order execution status"""
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class ProfitOpportunity:
    """Profit-optimized trading opportunity"""
    symbol: str
    strategy_tier: StrategyTier
    expected_value: float
    confidence_score: float
    risk_reward_ratio: float
    position_size_multiplier: float
    entry_price: float
    stop_loss: float
    target_price: float
    timeframe: str
    reasoning: str
    technical_confluence: Dict[str, float]


@dataclass
class TradeExecution:
    """Trade execution details"""
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: int
    entry_price: float
    stop_loss: float
    take_profit: float
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.PENDING
    fill_price: Optional[float] = None
    fill_time: Optional[datetime] = None
    pnl: Optional[float] = None


@dataclass
class PortfolioMonitoring:
    """Portfolio monitoring data"""
    total_value: float
    cash: float
    positions: List[Position]
    day_pnl: float
    total_pnl: float
    active_trades: int
    win_rate: float
    sharpe_ratio: Optional[float] = None


class ProfitMaximizationEngine:
    """Dynamic profit maximization with multi-tier strategies"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.confidence_thresholds = {
            "bull_market": 0.65,
            "bear_market": 0.75,
            "sideways": 0.70,
            "volatile": 0.80
        }
    
    async def scan_profit_opportunities(self, account_size: float, 
                                      max_positions: int = 8) -> List[ProfitOpportunity]:
        """Scan for profit-optimized opportunities across all strategy tiers"""
        
        try:
            self.logger.info("🎯 Scanning for profit optimization opportunities...")
            
            # Get market regime for dynamic threshold adjustment
            market_regime = await self._assess_market_regime()
            confidence_threshold = self.confidence_thresholds[market_regime]
            
            opportunities = []
            
            # Tier 1: Enhanced Momentum & TTM Squeeze
            tier1_ops = await self._scan_tier1_momentum()
            opportunities.extend(tier1_ops)
            
            # Filter by confidence threshold and expected value
            filtered_opportunities = [
                op for op in opportunities 
                if op.confidence_score >= confidence_threshold and op.expected_value > 0
            ]
            
            # Rank by expected value and diversification
            ranked_opportunities = self._rank_opportunities_by_ev(
                filtered_opportunities, account_size, max_positions
            )
            
            self.logger.info(f"📊 Found {len(ranked_opportunities)} profit-optimized opportunities")
            return ranked_opportunities[:max_positions]
            
        except Exception as e:
            self.logger.error(f"Error scanning profit opportunities: {e}")
            return []
    
    async def _assess_market_regime(self) -> str:
        """Assess current market regime"""
        # Simplified market regime detection
        # In production, this would analyze VIX, market trends, etc.
        return "sideways"  # Default to moderate threshold
    
    async def _scan_tier1_momentum(self) -> List[ProfitOpportunity]:
        """Scan for Tier 1 momentum and TTM Squeeze opportunities"""
        
        opportunities = []
        
        # Popular symbols for scanning
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMD", "SPY", "QQQ"]
        
        for symbol in symbols:
            try:
                # This would integrate with technical analysis
                # For now, creating sample opportunity
                opportunity = ProfitOpportunity(
                    symbol=symbol,
                    strategy_tier=StrategyTier.TIER_1_MOMENTUM,
                    expected_value=0.15,  # 15% expected return
                    confidence_score=0.72,
                    risk_reward_ratio=2.5,
                    position_size_multiplier=1.0,
                    entry_price=150.0,  # Placeholder
                    stop_loss=145.0,
                    target_price=160.0,
                    timeframe="1-3 days",
                    reasoning="TTM Squeeze firing with momentum confirmation",
                    technical_confluence={
                        "ttm_squeeze": 0.8,
                        "momentum": 0.7,
                        "volume": 0.6
                    }
                )
                opportunities.append(opportunity)
                
            except Exception as e:
                self.logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        return opportunities
    
    def _rank_opportunities_by_ev(self, opportunities: List[ProfitOpportunity],
                                 account_size: float, max_positions: int) -> List[ProfitOpportunity]:
        """Rank opportunities by expected value and diversification"""
        
        # Sort by expected value * confidence
        ranked = sorted(
            opportunities,
            key=lambda op: op.expected_value * op.confidence_score * op.risk_reward_ratio,
            reverse=True
        )
        
        return ranked


class SmartOrderRouter:
    """Intelligent order routing and execution optimization"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alpaca = tradeapi.REST(
            settings.APCA_API_KEY_ID,
            settings.APCA_API_SECRET_KEY,
            settings.APCA_API_BASE_URL,
            api_version='v2'
        )
    
    async def execute_trade(self, opportunity: ProfitOpportunity, 
                           position_size: float) -> TradeExecution:
        """Execute trade with smart order routing"""
        
        try:
            # Calculate quantity based on position size
            quantity = int(position_size / opportunity.entry_price)
            
            if quantity <= 0:
                raise ValueError("Position size too small for trade execution")
            
            # Create bracket order (entry + stop loss + take profit)
            order = self.alpaca.submit_order(
                symbol=opportunity.symbol,
                qty=quantity,
                side='buy',
                type='market',
                time_in_force='day',
                order_class='bracket',
                stop_loss={'stop_price': str(opportunity.stop_loss)},
                take_profit={'limit_price': str(opportunity.target_price)}
            )
            
            execution = TradeExecution(
                symbol=opportunity.symbol,
                side='buy',
                quantity=quantity,
                entry_price=opportunity.entry_price,
                stop_loss=opportunity.stop_loss,
                take_profit=opportunity.target_price,
                order_id=order.id,
                status=OrderStatus.PENDING
            )
            
            self.logger.info(f"✅ Trade executed: {opportunity.symbol} x{quantity}")
            return execution
            
        except Exception as e:
            self.logger.error(f"Error executing trade for {opportunity.symbol}: {e}")
            raise
    
    async def monitor_execution(self, execution: TradeExecution) -> TradeExecution:
        """Monitor trade execution status"""
        
        try:
            if not execution.order_id:
                return execution
            
            order = self.alpaca.get_order(execution.order_id)
            
            if order.status == 'filled':
                execution.status = OrderStatus.FILLED
                execution.fill_price = float(order.filled_avg_price or order.limit_price)
                execution.fill_time = datetime.fromisoformat(order.filled_at.replace('Z', '+00:00'))
            elif order.status == 'partially_filled':
                execution.status = OrderStatus.PARTIALLY_FILLED
            elif order.status == 'cancelled':
                execution.status = OrderStatus.CANCELLED
            elif order.status == 'rejected':
                execution.status = OrderStatus.REJECTED
            
            return execution
            
        except Exception as e:
            self.logger.error(f"Error monitoring execution for {execution.symbol}: {e}")
            return execution


class ExecutionMonitoringEngine:
    """Real-time execution and portfolio monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alpaca = tradeapi.REST(
            settings.APCA_API_KEY_ID,
            settings.APCA_API_SECRET_KEY,
            settings.APCA_API_BASE_URL,
            api_version='v2'
        )
        self.active_executions: Dict[str, TradeExecution] = {}
    
    async def monitor_portfolio(self) -> PortfolioMonitoring:
        """Monitor overall portfolio performance"""
        
        try:
            # Get account info
            account = self.alpaca.get_account()
            
            # Get positions
            positions = self.alpaca.list_positions()
            
            # Calculate metrics
            total_value = float(account.portfolio_value)
            cash = float(account.cash)
            day_pnl = float(account.unrealized_pl or 0)
            total_pnl = float(account.unrealized_pl or 0)
            
            # Convert positions
            position_list = []
            for pos in positions:
                position_list.append(Position(
                    symbol=pos.symbol,
                    qty=Decimal(pos.qty),
                    side=pos.side,
                    market_value=Decimal(pos.market_value),
                    cost_basis=Decimal(pos.cost_basis),
                    unrealized_pl=Decimal(pos.unrealized_pl),
                    unrealized_plpc=Decimal(pos.unrealized_plpc),
                    current_price=Decimal(pos.current_price),
                    lastday_price=Decimal(pos.lastday_price),
                    change_today=Decimal(pos.change_today)
                ))
            
            # Calculate win rate (simplified)
            win_rate = self._calculate_win_rate()
            
            return PortfolioMonitoring(
                total_value=total_value,
                cash=cash,
                positions=position_list,
                day_pnl=day_pnl,
                total_pnl=total_pnl,
                active_trades=len(position_list),
                win_rate=win_rate
            )
            
        except Exception as e:
            self.logger.error(f"Error monitoring portfolio: {e}")
            return PortfolioMonitoring(
                total_value=0.0,
                cash=0.0,
                positions=[],
                day_pnl=0.0,
                total_pnl=0.0,
                active_trades=0,
                win_rate=0.0
            )
    
    def _calculate_win_rate(self) -> float:
        """Calculate win rate from recent trades"""
        # Simplified win rate calculation
        # In production, this would analyze closed positions
        return 0.65  # Placeholder
    
    async def monitor_active_trades(self) -> Dict[str, TradeExecution]:
        """Monitor all active trade executions"""
        
        updated_executions = {}
        
        for symbol, execution in self.active_executions.items():
            if execution.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]:
                # Update execution status
                try:
                    order = self.alpaca.get_order(execution.order_id)
                    
                    if order.status == 'filled':
                        execution.status = OrderStatus.FILLED
                        execution.fill_price = float(order.filled_avg_price or order.limit_price)
                        execution.fill_time = datetime.fromisoformat(order.filled_at.replace('Z', '+00:00'))
                        
                        # Calculate P&L if filled
                        if execution.side == 'buy':
                            current_price = self._get_current_price(symbol)
                            execution.pnl = (current_price - execution.fill_price) * execution.quantity
                
                except Exception as e:
                    self.logger.error(f"Error updating execution for {symbol}: {e}")
            
            updated_executions[symbol] = execution
        
        self.active_executions = updated_executions
        return updated_executions
    
    def _get_current_price(self, symbol: str) -> float:
        """Get current price for P&L calculation"""
        try:
            quote = self.alpaca.get_latest_quote(symbol)
            return float(quote.ask_price)
        except:
            return 0.0
    
    def add_execution(self, execution: TradeExecution):
        """Add execution to monitoring"""
        self.active_executions[execution.symbol] = execution
    
    def remove_execution(self, symbol: str):
        """Remove execution from monitoring"""
        if symbol in self.active_executions:
            del self.active_executions[symbol]


class TradeRecyclingEngine:
    """Trade recycling for capital efficiency"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.recycling_threshold = 0.02  # 2% profit threshold for recycling
    
    async def identify_recycling_opportunities(self, portfolio: PortfolioMonitoring) -> List[Dict[str, Any]]:
        """Identify positions ready for profit recycling"""
        
        recycling_opportunities = []
        
        for position in portfolio.positions:
            unrealized_pl_percent = float(position.unrealized_plpc)
            
            # Check if position meets recycling criteria
            if unrealized_pl_percent >= self.recycling_threshold:
                opportunity = {
                    "symbol": position.symbol,
                    "current_profit": float(position.unrealized_pl),
                    "profit_percent": unrealized_pl_percent,
                    "recommendation": "RECYCLE",
                    "reasoning": f"Position up {unrealized_pl_percent*100:.1f}% - consider taking profits and redeploying capital"
                }
                recycling_opportunities.append(opportunity)
        
        return recycling_opportunities
    
    async def execute_recycling(self, symbol: str, quantity: int) -> bool:
        """Execute trade recycling (sell position)"""
        
        try:
            # This would integrate with the smart order router
            self.logger.info(f"🔄 Recycling position: {symbol} x{quantity}")
            
            # In production, this would:
            # 1. Sell the position
            # 2. Wait for fill
            # 3. Redeploy capital to new opportunities
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error recycling {symbol}: {e}")
            return False


class AtlasTradingEngine:
    """Main trading engine coordinating all trading functionality"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.profit_engine = ProfitMaximizationEngine()
        self.order_router = SmartOrderRouter()
        self.execution_monitor = ExecutionMonitoringEngine()
        self.recycling_engine = TradeRecyclingEngine()
    
    async def execute_profit_optimization_strategy(self, account_size: float) -> Dict[str, Any]:
        """Execute complete profit optimization strategy"""
        
        try:
            # 1. Scan for profit opportunities
            opportunities = await self.profit_engine.scan_profit_opportunities(account_size)
            
            # 2. Monitor current portfolio
            portfolio = await self.execution_monitor.monitor_portfolio()
            
            # 3. Check for recycling opportunities
            recycling_ops = await self.recycling_engine.identify_recycling_opportunities(portfolio)
            
            # 4. Execute top opportunities (if any)
            executed_trades = []
            for opportunity in opportunities[:3]:  # Limit to top 3
                try:
                    position_size = account_size * 0.05  # 5% per position
                    execution = await self.order_router.execute_trade(opportunity, position_size)
                    self.execution_monitor.add_execution(execution)
                    executed_trades.append(execution)
                except Exception as e:
                    self.logger.error(f"Failed to execute {opportunity.symbol}: {e}")
            
            return {
                "opportunities_found": len(opportunities),
                "trades_executed": len(executed_trades),
                "recycling_opportunities": len(recycling_ops),
                "portfolio_value": portfolio.total_value,
                "active_positions": portfolio.active_trades,
                "win_rate": portfolio.win_rate,
                "executed_trades": [
                    {
                        "symbol": trade.symbol,
                        "quantity": trade.quantity,
                        "entry_price": trade.entry_price,
                        "stop_loss": trade.stop_loss,
                        "take_profit": trade.take_profit
                    }
                    for trade in executed_trades
                ],
                "recycling_recommendations": recycling_ops
            }
            
        except Exception as e:
            self.logger.error(f"Error in profit optimization strategy: {e}")
            return {
                "error": str(e),
                "opportunities_found": 0,
                "trades_executed": 0
            }
    
    async def get_portfolio_status(self) -> Dict[str, Any]:
        """Get comprehensive portfolio status"""
        
        portfolio = await self.execution_monitor.monitor_portfolio()
        active_trades = await self.execution_monitor.monitor_active_trades()
        
        return {
            "portfolio": {
                "total_value": portfolio.total_value,
                "cash": portfolio.cash,
                "day_pnl": portfolio.day_pnl,
                "total_pnl": portfolio.total_pnl,
                "active_trades": portfolio.active_trades,
                "win_rate": portfolio.win_rate
            },
            "positions": [
                {
                    "symbol": pos.symbol,
                    "quantity": float(pos.qty),
                    "market_value": float(pos.market_value),
                    "unrealized_pl": float(pos.unrealized_pl),
                    "unrealized_pl_percent": float(pos.unrealized_plpc)
                }
                for pos in portfolio.positions
            ],
            "active_executions": [
                {
                    "symbol": exec.symbol,
                    "status": exec.status.value,
                    "quantity": exec.quantity,
                    "entry_price": exec.entry_price,
                    "pnl": exec.pnl
                }
                for exec in active_trades.values()
            ]
        }
