"""
Streamlined A.T.L.A.S Trading System - Data Models
Consolidated from multiple model files into single comprehensive module
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


# Enums
class OrderSide(str, Enum):
    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(str, Enum):
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"


class TimeFrame(str, Enum):
    MINUTE_1 = "1Min"
    MINUTE_5 = "5Min"
    MINUTE_15 = "15Min"
    MINUTE_30 = "30Min"
    HOUR_1 = "1Hour"
    DAY_1 = "1Day"


class SignalType(str, Enum):
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


# Market Data Models
class Quote(BaseModel):
    """Real-time stock quote"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None


class OHLCV(BaseModel):
    """OHLCV candlestick data"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    symbol: str


class TechnicalIndicators(BaseModel):
    """Technical analysis indicators"""
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    sma_20: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    atr: Optional[float] = None
    volume_sma: Optional[float] = None


# Trading Models
class Order(BaseModel):
    """Trading order"""
    id: Optional[str] = None
    symbol: str
    qty: float
    side: OrderSide
    order_type: OrderType
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.NEW
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    filled_qty: float = 0
    filled_avg_price: Optional[float] = None


class Position(BaseModel):
    """Trading position"""
    symbol: str
    qty: float
    side: str
    market_value: float
    cost_basis: float
    unrealized_pl: float
    unrealized_plpc: float
    current_price: float
    lastday_price: float
    change_today: float


class TradingSignal(BaseModel):
    """Trading signal from technical analysis"""
    symbol: str
    signal_type: SignalType
    confidence: float
    entry_price: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    timeframe: TimeFrame
    indicators: Optional[TechnicalIndicators] = None
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ScanResult(BaseModel):
    """Technical analysis scan result"""
    symbol: str
    scan_type: str
    score: float
    current_price: float
    indicators: Dict[str, float]
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Portfolio Models
class PortfolioMetrics(BaseModel):
    """Portfolio performance metrics"""
    total_value: float
    cash: float
    buying_power: float
    day_pl: float
    day_pl_percent: float
    total_pl: float
    total_pl_percent: float
    positions_count: int
    last_updated: datetime = Field(default_factory=datetime.utcnow)


# News and Sentiment Models
class NewsArticle(BaseModel):
    """News article"""
    title: str
    content: Optional[str] = None
    url: str
    source: str
    published_at: datetime
    sentiment_score: Optional[float] = None
    symbols: List[str] = []


class SentimentAnalysis(BaseModel):
    """Sentiment analysis result"""
    symbol: str
    overall_sentiment: str
    score: float
    confidence: float
    news_count: int
    key_themes: List[str]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# AI and Chat Models
class ChatMessage(BaseModel):
    """Chat message"""
    role: str  # "user" or "assistant"
    content: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    function_call: Optional[Dict[str, Any]] = None
    function_result: Optional[Dict[str, Any]] = None


class AIResponse(BaseModel):
    """AI response with trading context"""
    response: str
    type: str = "chat"
    requires_action: bool = False
    trading_plan: Optional[Dict[str, Any]] = None
    function_called: Optional[str] = None
    confidence: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# RAG Education Models
class BookContent(BaseModel):
    """Trading book content for RAG"""
    book_title: str
    chapter: str
    section: str
    content: str
    concepts: List[str]
    embedding_id: Optional[str] = None


class EducationQuery(BaseModel):
    """Educational query for RAG system"""
    question: str
    context: Optional[str] = None
    book_filter: Optional[str] = None
    concept_filter: Optional[List[str]] = None


class EducationResponse(BaseModel):
    """Educational response from RAG system"""
    answer: str
    sources: List[str]
    book_references: List[str]
    confidence: float
    related_concepts: List[str]


# API Request/Response Models
class QuoteRequest(BaseModel):
    """Request for stock quote"""
    symbol: str
    include_indicators: bool = False


class ScanRequest(BaseModel):
    """Request for technical scan"""
    scan_type: str
    filters: Optional[Dict[str, Any]] = None
    limit: int = 10


class ChatRequest(BaseModel):
    """Chat request"""
    message: str
    context: Optional[Dict[str, Any]] = None
    include_market_data: bool = True


class OrderRequest(BaseModel):
    """Order placement request"""
    symbol: str
    qty: float
    side: OrderSide
    order_type: OrderType
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None
    target_price: Optional[float] = None  # For bracket orders


# Event and Explanation Models
class MarketEvent(BaseModel):
    """Market event for explanation"""
    symbol: str
    event_type: str
    description: str
    price_impact: float
    timestamp: datetime
    news_articles: List[NewsArticle] = []


class EventExplanation(BaseModel):
    """Explanation of market event"""
    event: MarketEvent
    explanation: str
    contributing_factors: List[str]
    market_impact: str
    trading_implications: str
    confidence: float


# Chain-of-Thought Models
class ChainOfThoughtStep(BaseModel):
    """Individual step in chain-of-thought analysis"""
    step_number: int
    category: str  # "technical", "momentum", "volume", "risk", "final"
    title: str
    explanation: str
    analogy: Optional[str] = None
    technical_values: Dict[str, float] = {}
    confidence_contribution: float = 0.0
    beginner_friendly: bool = True


class ChainOfThoughtAnalysis(BaseModel):
    """Complete chain-of-thought analysis for a trading signal"""
    symbol: str
    analysis_type: str = "ttm_squeeze"
    steps: List[ChainOfThoughtStep]
    final_confidence: float
    final_recommendation: str
    risk_assessment: str
    educational_notes: List[str] = []
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ProfitTargetedStrategy(BaseModel):
    """Profit-targeted trading strategy"""
    strategy_id: str
    profit_target: float
    timeframe: str  # "intraday", "swing", "position"
    max_risk_per_trade: float
    max_risk_percent: float = 2.0
    target_positions: int
    sector_diversification: bool = True
    symbols_to_scan: List[str]
    strategy_reasoning: str
    kelly_fraction: Optional[float] = None
    expected_win_rate: float = 0.65  # Historical TTM Squeeze win rate


class RiskManagementProfile(BaseModel):
    """Risk management configuration for trading"""
    account_size: float
    daily_loss_limit_percent: float = 3.0
    max_correlation: float = 0.85
    volatility_threshold: float = 40.0  # VIX threshold
    min_confidence_threshold: float = 0.70
    paper_trading_required: bool = True
    position_sizing_method: str = "kelly_criterion"


class OptionsEducationContext(BaseModel):
    """Educational context for options trading"""
    concept: str  # "theta_decay", "iv_crush", "greeks", "assignment_risk"
    simple_explanation: str
    analogy: str
    warning_conditions: List[str] = []
    educational_tips: List[str] = []


# Configuration Models
class TradingConfig(BaseModel):
    """Trading configuration"""
    default_risk_percent: float = 2.0
    max_positions: int = 10
    paper_trading: bool = True
    auto_stop_loss: bool = True
    position_sizing_method: str = "risk_based"


class SystemConfig(BaseModel):
    """System configuration"""
    trading: TradingConfig
    api_timeout: int = 30
    cache_ttl: int = 300
    max_scan_results: int = 50
    enable_rag: bool = True
    log_level: str = "INFO"
