"""
Quick test to validate mentor-style fixes
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mentor_response():
    """Test that mentor responses come before safety blocks"""
    
    try:
        from streamlined.conversational_cot_interface import ConversationalCoTInterface
        
        print("🧪 Testing Mentor-Style Response Fix...")
        
        interface = ConversationalCoTInterface()
        
        # Test the problematic scenario
        test_message = "Make me $50 today"
        user_context = {"account_size": 25000}
        
        print(f"User: {test_message}")
        print("Processing...")
        
        response = await interface.process_user_message(test_message, user_context)
        
        print(f"\nA.T.L.A.S Response:")
        print("=" * 50)
        print(response.response)
        print("=" * 50)
        
        # Validate mentor-style elements
        checks = {
            "Has mentor emoji (🎯)": "🎯" in response.response,
            "Contains 'understand'": "understand" in response.response.lower(),
            "Has reality check": any(word in response.response.lower() for word in ["reality check", "professional", "instead"]),
            "Contains analogy": "like" in response.response.lower(),
            "Not blocked message": not response.response.startswith("🚫"),
            "Educational guidance": any(word in response.response.lower() for word in ["learn", "step", "approach"])
        }
        
        print("\n📊 Validation Results:")
        passed = 0
        for check, result in checks.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {check}")
            if result:
                passed += 1
        
        success_rate = (passed / len(checks)) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 MENTOR-STYLE FIX SUCCESSFUL!")
            return True
        else:
            print("⚠️ Mentor-style fix needs more work")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multi_agent_integration():
    """Test multi-agent system integration"""
    
    try:
        from streamlined.cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator
        
        print("\n🤖 Testing Multi-Agent Integration...")
        
        orchestrator = ChainOfThoughtTradingOrchestrator(mentor_mode=True)
        
        # Test multi-agent analysis
        result = await orchestrator.execute_multi_agent_analysis("AAPL", 25000)
        
        if result["success"]:
            print("✅ Multi-agent analysis working")
            print(f"✅ Consensus: {result['agent_consensus']['final_decision']}")
            print(f"✅ Confidence: {result['agent_consensus']['consensus_confidence']:.1f}")
            return True
        else:
            print("❌ Multi-agent analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Multi-agent test failed: {e}")
        return False

async def test_final_vision_scenario():
    """Test the complete final vision scenario"""

    try:
        from streamlined.conversational_cot_interface import ConversationalCoTInterface
        from streamlined.cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator

        print("\n🎯 Testing Final Vision Scenario...")
        print("User: 'I want to make $100 this week without blowing up.'")

        interface = ConversationalCoTInterface()
        orchestrator = ChainOfThoughtTradingOrchestrator(mentor_mode=True)

        # Process the user request
        user_message = "I want to make $100 this week without blowing up."
        user_context = {"account_size": 25000}

        # Get conversational response
        response = await interface.process_user_message(user_message, user_context)

        print(f"\nA.T.L.A.S Response Preview:")
        print("=" * 60)
        print(response.response[:500] + "..." if len(response.response) > 500 else response.response)
        print("=" * 60)

        # Validate final vision components
        validation_checks = {
            "Mentor-Style Response": "🎯" in response.response and "understand" in response.response.lower(),
            "Reality Check with Analogy": "like" in response.response.lower() and any(word in response.response.lower() for word in ["realistic", "professional", "instead"]),
            "Educational Guidance": any(word in response.response.lower() for word in ["plan", "strategy", "approach", "learn", "step"]),
            "No Hard Blocking": not response.response.startswith("🚫"),
            "Specific Actionable Steps": any(word in response.response.lower() for word in ["next steps", "ask me", "try", "show"])
        }

        print("\n📊 Final Vision Validation:")
        passed = 0
        for check, result in validation_checks.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {check}")
            if result:
                passed += 1

        success_rate = (passed / len(validation_checks)) * 100
        print(f"\n🎯 Final Vision Success Rate: {success_rate:.1f}%")

        return success_rate >= 80

    except Exception as e:
        print(f"❌ Final vision test failed: {e}")
        return False

async def main():
    """Run comprehensive enhanced system validation"""

    print("🚀 A.T.L.A.S Enhanced System - Complete Validation")
    print("=" * 60)

    # Test mentor response fix
    mentor_success = await test_mentor_response()

    # Test multi-agent integration
    agent_success = await test_multi_agent_integration()

    # Test final vision scenario
    vision_success = await test_final_vision_scenario()

    print("\n" + "=" * 60)
    print("🏁 COMPREHENSIVE VALIDATION RESULTS")
    print("=" * 60)

    all_success = mentor_success and agent_success and vision_success

    if all_success:
        print("🎉 ALL ENHANCEMENTS SUCCESSFUL!")
        print("✅ Mentor-style responses working perfectly")
        print("✅ Multi-agent system fully operational")
        print("✅ Final vision scenario validated")
        print("🚀 A.T.L.A.S Enhanced Edition is READY!")
        print("\n🌟 The world's first conversational AI trading mentor is live!")
        return True
    else:
        print("⚠️ Some components need attention:")
        if not mentor_success:
            print("❌ Mentor-style responses need fixing")
        if not agent_success:
            print("❌ Multi-agent system needs fixing")
        if not vision_success:
            print("❌ Final vision scenario needs work")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎊 Ready to test the final vision scenario!")
    else:
        print("\n🔧 Additional fixes needed before final validation.")
