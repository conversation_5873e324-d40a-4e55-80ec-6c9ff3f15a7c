"""
A.T.L.A.S Enhanced System Validation
Quick validation of all enhancements and fixes
"""

import os
import sys

def validate_file_structure():
    """Validate that all enhanced files are present"""
    
    print("🔍 Validating Enhanced File Structure...")
    
    required_files = [
        "streamlined/conversational_cot_interface.py",
        "streamlined/cot_trading_orchestrator.py", 
        "streamlined/multi_agent_system.py",
        "streamlined/rl_execution_engine.py",
        "streamlined/compliance_audit_system.py",
        "streamlined/performance_optimizer.py",
        "streamlined/predicto_integration.py",
        "streamlined/chain_of_thought_engine.py",
        "streamlined/profit_strategy_engine.py",
        "streamlined/risk_management_engine.py",
        "streamlined/safety_guardrails.py"
    ]
    
    missing_files = []
    present_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            present_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    print(f"\n📊 File Structure Validation:")
    print(f"✅ Present: {len(present_files)}/{len(required_files)}")
    print(f"❌ Missing: {len(missing_files)}")
    
    return len(missing_files) == 0

def validate_imports():
    """Validate that key imports work"""
    
    print("\n🔍 Validating Enhanced Imports...")
    
    import_tests = [
        ("Conversational Interface", "from streamlined.conversational_cot_interface import ConversationalCoTInterface"),
        ("Trading Orchestrator", "from streamlined.cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator"),
        ("Multi-Agent System", "from streamlined.multi_agent_system import MultiAgentCoordinator"),
        ("RL Execution", "from streamlined.rl_execution_engine import SmartOrderRouter"),
        ("Compliance System", "from streamlined.compliance_audit_system import ComplianceEngine"),
        ("Performance Optimizer", "from streamlined.performance_optimizer import PerformanceOptimizer"),
        ("Predicto Integration", "from streamlined.predicto_integration import PredictoAPIIntegration")
    ]
    
    successful_imports = 0
    failed_imports = []
    
    for name, import_statement in import_tests:
        try:
            exec(import_statement)
            print(f"✅ {name}")
            successful_imports += 1
        except Exception as e:
            print(f"❌ {name}: {str(e)}")
            failed_imports.append((name, str(e)))
    
    print(f"\n📊 Import Validation:")
    print(f"✅ Successful: {successful_imports}/{len(import_tests)}")
    print(f"❌ Failed: {len(failed_imports)}")
    
    if failed_imports:
        print("\n🔧 Failed Import Details:")
        for name, error in failed_imports:
            print(f"   {name}: {error}")
    
    return len(failed_imports) == 0

def validate_key_features():
    """Validate key feature implementations"""
    
    print("\n🔍 Validating Key Features...")
    
    feature_checks = []
    
    try:
        # Check mentor-style responses
        with open("streamlined/conversational_cot_interface.py", "r") as f:
            content = f.read()
            has_mentor_analysis = "_provide_mentor_analysis" in content
            has_educational_alternative = "_create_educational_alternative" in content
            feature_checks.append(("Mentor-Style Responses", has_mentor_analysis and has_educational_alternative))
    except:
        feature_checks.append(("Mentor-Style Responses", False))
    
    try:
        # Check multi-agent system
        with open("streamlined/multi_agent_system.py", "r") as f:
            content = f.read()
            has_agents = all(agent in content for agent in ["TechnicalAnalysisAgent", "RiskManagementAgent", "SentimentAnalysisAgent", "ExecutionTimingAgent"])
            has_coordinator = "MultiAgentCoordinator" in content
            feature_checks.append(("Multi-Agent System", has_agents and has_coordinator))
    except:
        feature_checks.append(("Multi-Agent System", False))
    
    try:
        # Check RL execution
        with open("streamlined/rl_execution_engine.py", "r") as f:
            content = f.read()
            has_rl_agent = "RLExecutionAgent" in content
            has_smart_router = "SmartOrderRouter" in content
            feature_checks.append(("RL-Optimized Execution", has_rl_agent and has_smart_router))
    except:
        feature_checks.append(("RL-Optimized Execution", False))
    
    try:
        # Check compliance system
        with open("streamlined/compliance_audit_system.py", "r") as f:
            content = f.read()
            has_compliance_engine = "ComplianceEngine" in content
            has_audit_trail = "audit_trail" in content
            feature_checks.append(("Compliance & Audit", has_compliance_engine and has_audit_trail))
    except:
        feature_checks.append(("Compliance & Audit", False))
    
    try:
        # Check performance optimization
        with open("streamlined/performance_optimizer.py", "r") as f:
            content = f.read()
            has_performance_monitor = "PerformanceMonitor" in content
            has_cache_manager = "CacheManager" in content
            feature_checks.append(("Performance Optimization", has_performance_monitor and has_cache_manager))
    except:
        feature_checks.append(("Performance Optimization", False))
    
    # Display results
    successful_features = 0
    for feature_name, is_present in feature_checks:
        if is_present:
            print(f"✅ {feature_name}")
            successful_features += 1
        else:
            print(f"❌ {feature_name}")
    
    print(f"\n📊 Feature Validation:")
    print(f"✅ Implemented: {successful_features}/{len(feature_checks)}")
    print(f"❌ Missing: {len(feature_checks) - successful_features}")
    
    return successful_features == len(feature_checks)

def validate_integration():
    """Validate that components are properly integrated"""
    
    print("\n🔍 Validating Component Integration...")
    
    integration_checks = []
    
    try:
        # Check orchestrator integration
        with open("streamlined/cot_trading_orchestrator.py", "r") as f:
            content = f.read()
            has_multi_agent = "MultiAgentCoordinator" in content
            has_rl_execution = "SmartOrderRouter" in content
            has_compliance = "ComplianceEngine" in content
            has_performance = "PerformanceOptimizer" in content
            integration_checks.append(("Orchestrator Integration", has_multi_agent and has_rl_execution and has_compliance and has_performance))
    except:
        integration_checks.append(("Orchestrator Integration", False))
    
    try:
        # Check conversational interface integration
        with open("streamlined/conversational_cot_interface.py", "r") as f:
            content = f.read()
            has_mentor_methods = "_provide_mentor_analysis" in content and "_create_educational_alternative" in content
            has_safety_integration = "safety_assessment" in content
            integration_checks.append(("Conversational Integration", has_mentor_methods and has_safety_integration))
    except:
        integration_checks.append(("Conversational Integration", False))
    
    # Display results
    successful_integrations = 0
    for integration_name, is_integrated in integration_checks:
        if is_integrated:
            print(f"✅ {integration_name}")
            successful_integrations += 1
        else:
            print(f"❌ {integration_name}")
    
    print(f"\n📊 Integration Validation:")
    print(f"✅ Integrated: {successful_integrations}/{len(integration_checks)}")
    print(f"❌ Not Integrated: {len(integration_checks) - successful_integrations}")
    
    return successful_integrations == len(integration_checks)

def main():
    """Run comprehensive validation"""
    
    print("🚀 A.T.L.A.S Enhanced System Validation")
    print("=" * 60)
    
    # Run all validations
    file_structure_ok = validate_file_structure()
    imports_ok = validate_imports()
    features_ok = validate_key_features()
    integration_ok = validate_integration()
    
    # Calculate overall score
    total_checks = 4
    passed_checks = sum([file_structure_ok, imports_ok, features_ok, integration_ok])
    success_rate = (passed_checks / total_checks) * 100
    
    print("\n" + "=" * 60)
    print("🏁 VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"📁 File Structure: {'✅ PASS' if file_structure_ok else '❌ FAIL'}")
    print(f"📦 Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"🎯 Features: {'✅ PASS' if features_ok else '❌ FAIL'}")
    print(f"🔗 Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 100:
        print("🎉 PERFECT! All enhancements validated successfully!")
        print("🚀 A.T.L.A.S Enhanced Edition is ready for deployment!")
        print("\n🌟 Features Ready:")
        print("   • Conversational AI mentor with reality checks")
        print("   • Multi-agent institutional-grade analysis")
        print("   • RL-optimized execution with smart routing")
        print("   • Regulatory compliance and audit trails")
        print("   • Performance optimization and monitoring")
        print("   • Enhanced market predictions with Predicto")
    elif success_rate >= 75:
        print("✅ GOOD! Most enhancements are working correctly.")
        print("🔧 Minor fixes may be needed for full functionality.")
    elif success_rate >= 50:
        print("⚠️ PARTIAL! Some enhancements need attention.")
        print("🔧 Review failed components and fix issues.")
    else:
        print("❌ CRITICAL! Major issues detected.")
        print("🔧 Significant fixes required before deployment.")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎊 A.T.L.A.S Enhanced Edition validation complete!")
        print("Ready for the revolutionary AI trading experience!")
    else:
        print("\n🔧 Additional work needed before full deployment.")
    
    sys.exit(0 if success else 1)
